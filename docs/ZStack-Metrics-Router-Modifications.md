# ZStack指标收集模块多平台路由修改总结

## 修改概述

我已经成功修改了`src/main/java/cn/coder/zj/module/collector/collect/metrics`目录下所有ZStack相关的指标收集文件，将其中的SDK API调用包装在`ZStackMultiPlatformRouter.executeWithPlatform()`方法中，以实现多平台配置隔离。

## 修改的文件列表

### 1. CPU指标收集
- **文件**: `src/main/java/cn/coder/zj/module/collector/collect/metrics/cpu/ZsTackCpuImpl.java`
- **主要API**: `GetMetricDataAction` (CPU平均使用率)
- **指标类型**: `CPUAverageUsedUtilization`
- **资源类型**: VM和Host

### 2. 磁盘指标收集
- **文件**: `src/main/java/cn/coder/zj/module/collector/collect/metrics/disk/ZsTackDiskImpl.java`
- **主要API**: `GetMetricDataAction`, `QueryPrimaryStorageAction`
- **指标类型**: 
  - 磁盘读写速度
  - 磁盘读写OPS
  - 磁盘使用率
- **资源类型**: VM、Host、存储

### 3. 内存指标收集
- **文件**: `src/main/java/cn/coder/zj/module/collector/collect/metrics/mem/ZsTackMemImpl.java`
- **主要API**: `GetMetricDataAction`
- **指标类型**:
  - 内存使用量
  - 内存空闲量
  - 内存使用率
- **资源类型**: VM和Host

### 4. 网络指标收集
- **文件**: `src/main/java/cn/coder/zj/module/collector/collect/metrics/net/ZsTackNetImpl.java`
- **主要API**: `GetMetricDataAction`
- **指标类型**:
  - 网络入流量
  - 网络出流量
- **资源类型**: VM和Host

## 修改模式

对每个文件都采用了相同的修改模式：

### 1. 添加导入语句
```java
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
```

### 2. 包装主要收集方法
```java
// 修改前
for (Object o : platformList) {
    taskExecutor.execute(() -> {
        Platform platform = (Platform) o;
        String token = platform.getZsTackPlatform().getToken();
        // 直接调用SDK API
        GetMetricDataAction action = new GetMetricDataAction();
        action.sessionId = token;
        GetMetricDataAction.Result result = action.call();
        // 处理结果...
    });
}

// 修改后
for (Object o : platformList) {
    taskExecutor.execute(() -> {
        try {
            Platform platform = (Platform) o;
            // 使用ZStackMultiPlatformRouter确保多平台配置隔离
            ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                String token = platform.getZsTackPlatform().getToken();
                if (token == null && platform.getAkType() == 0) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return null;
                }
                
                // 在正确的平台配置下调用SDK API
                GetMetricDataAction action = new GetMetricDataAction();
                action.sessionId = token;
                GetMetricDataAction.Result result = action.call();
                // 处理结果...
                
                return metricDataList;
            });
        } catch (Exception e) {
            log.error("平台 {} 指标收集失败: {}", ((Platform) o).getPlatformName(), e.getMessage(), e);
        }
    });
}
```

### 3. 改进的错误处理
- 添加了适当的异常处理，捕获Router可能抛出的RuntimeException
- 改进了空值检查逻辑，区分不同认证类型
- 添加了更详细的错误日志

## 技术优势

### 1. 配置隔离
- 每个平台的指标收集都在独立的配置上下文中执行
- 避免了多平台并发时的ZSClient配置冲突

### 2. 线程安全
- 使用平台级别的锁机制确保并发安全
- 避免了竞态条件导致的数据错误

### 3. 性能优化
- 智能配置切换，只在平台真正切换时才重新配置
- 配置缓存机制减少重复配置开销

### 4. 向后兼容
- 保持了原有的方法签名和业务逻辑
- 不影响现有的指标收集流程

## 指标收集流程

### 修改后的执行流程
1. **平台遍历**: 遍历所有ZStack平台
2. **异步执行**: 为每个平台创建异步任务
3. **配置隔离**: 使用Router确保正确的平台配置
4. **指标收集**: 在正确配置下调用ZStack SDK API
5. **数据处理**: 处理返回的指标数据
6. **消息发送**: 发送收集到的指标数据

### 支持的认证方式
- **Session认证** (akType = 0): 使用sessionId
- **AccessKey认证** (akType = 1): 使用accessKeyId和accessKeySecret

## 错误处理改进

### 1. 空值检查优化
```java
// 修改前
if (token == null) {
    log.error("平台 {} token为空", platform.getPlatformName());
    return;
}

// 修改后
if (token == null && platform.getAkType() == 0) {
    log.error("平台 {} token为空", platform.getPlatformName());
    return null;
}
```

### 2. 异常处理增强
```java
try {
    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
        // 指标收集逻辑
        return metricDataList;
    });
} catch (Exception e) {
    log.error("平台 {} 指标收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
}
```

## 验证要点

### 1. 功能验证
- 确保所有指标类型都能正常收集
- 验证多平台并发收集的正确性
- 检查不同认证方式的兼容性

### 2. 性能验证
- 监控配置切换频率
- 测量指标收集响应时间
- 验证并发性能表现

### 3. 稳定性验证
- 长时间运行稳定性测试
- 异常情况下的恢复能力
- 内存泄漏检查

## 部署建议

### 1. 分阶段验证
1. **单平台测试**: 先在单个ZStack平台上验证
2. **多平台测试**: 逐步增加平台数量进行测试
3. **并发测试**: 验证高并发场景下的稳定性
4. **生产部署**: 在生产环境中逐步推广

### 2. 监控指标
- 指标收集成功率
- 配置切换次数
- 异常发生频率
- 响应时间分布

## 总结

通过将ZStack指标收集模块的SDK API调用包装在`ZStackMultiPlatformRouter.executeWithPlatform()`中，我们成功解决了多平台并发时的配置冲突问题，同时保持了原有功能的完整性和性能。

这些修改确保了：
- ✅ **配置隔离**: 每个平台的指标收集都在正确的配置下执行
- ✅ **线程安全**: 避免了多线程环境下的竞态条件
- ✅ **性能优化**: 智能配置切换减少不必要的开销
- ✅ **向后兼容**: 保持原有业务逻辑不变
- ✅ **错误处理**: 增强了异常处理和错误恢复能力

现在ZStack指标收集模块可以安全地在多平台环境下并发运行，不再受到全局配置冲突的影响。
