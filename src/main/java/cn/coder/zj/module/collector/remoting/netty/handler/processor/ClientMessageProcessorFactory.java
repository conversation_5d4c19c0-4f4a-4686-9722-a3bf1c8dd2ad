/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.coder.zj.module.collector.remoting.netty.handler.processor;

import cn.coder.zj.module.collector.job.service.ScheduleTaskService;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.coder.zj.module.collector.service.autoDiscovery.AutoDiscoveryService;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 客户端消息处理器工厂
 * 
 * <AUTHOR>
 */
@Slf4j
public class ClientMessageProcessorFactory {
    
    private final ConcurrentMap<ClusterMsg.MessageType, ClientMessageProcessor> processors = new ConcurrentHashMap<>();
    
    public ClientMessageProcessorFactory(TaskExecutor taskExecutor,
                                       ScheduleTaskService scheduleTaskService,
                                       CollectorConfig collectorConfig,
                                       AutoDiscoveryService autoDiscoveryService) {
        initializeProcessors(taskExecutor, scheduleTaskService, collectorConfig, autoDiscoveryService);
    }
    
    /**
     * 初始化处理器映射
     */
    private void initializeProcessors(TaskExecutor taskExecutor,
                                    ScheduleTaskService scheduleTaskService,
                                    CollectorConfig collectorConfig,
                                    AutoDiscoveryService autoDiscoveryService) {
        
        // 创建通用处理器
        ClientMessageProcessor universalProcessor = new UniversalClientProcessor(
                taskExecutor, scheduleTaskService, collectorConfig, autoDiscoveryService);
        
        // 为所有消息类型注册同一个处理器
        processors.put(ClusterMsg.MessageType.INFO, universalProcessor);
        processors.put(ClusterMsg.MessageType.UPDATE, universalProcessor);
        processors.put(ClusterMsg.MessageType.SCHEDULED_START, universalProcessor);
        processors.put(ClusterMsg.MessageType.SCHEDULED_INFO, universalProcessor);
        processors.put(ClusterMsg.MessageType.ONLINE, universalProcessor);
        processors.put(ClusterMsg.MessageType.OFFLINE, universalProcessor);
        processors.put(ClusterMsg.MessageType.OTHER, universalProcessor);
    }
    
    /**
     * 根据消息类型获取对应的处理器
     */
    public ClientMessageProcessor getProcessor(ClusterMsg.MessageType messageType) {
        return processors.get(messageType);
    }
    
    /**
     * 注册自定义处理器
     */
    public void registerProcessor(ClusterMsg.MessageType messageType, ClientMessageProcessor processor) {
        processors.put(messageType, processor);
        log.info("[客户端处理器工厂] 注册自定义处理器，消息类型: {}, 处理器: {}", 
                messageType, processor.getClass().getSimpleName());
    }
    
    /**
     * 检查是否支持指定的消息类型
     */
    public boolean supports(ClusterMsg.MessageType messageType) {
        return processors.containsKey(messageType);
    }
} 