package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.QueryVolumeSnapshotAction;
import org.zstack.sdk.VolumeSnapshotInventory;

import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_SNAPSHOT;

@Slf4j
public class ZsTackBasicSnapshotDataImpl extends AbstractBasicData {


    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformInfo : platformList) {
            Platform platform =(Platform) platformInfo;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                try {
                    // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        List<VolumeSnapshotData> dataList = new ArrayList<>();

                        String token = platform.getZsTackPlatform().getToken();
                        if (token == null && platform.getAkType() == 0) {
                            log.error("平台 {} token为空", platform.getPlatformName());
                            return null;
                        }

                        QueryVolumeSnapshotAction action = new QueryVolumeSnapshotAction();
                        action.conditions = new ArrayList<>();
                        if (platform.getAkType() == 0) {
                            action.sessionId = token;
                        } else {
                            action.accessKeyId = platform.getUsername();
                            action.accessKeySecret = platform.getPassword();
                        }

                        QueryVolumeSnapshotAction.Result res = action.call();
                        List snapshots = res.value.getInventories();
                        for (Object snapshot : snapshots) {
                            if (snapshot instanceof VolumeSnapshotInventory) {
                                VolumeSnapshotData volumeSnapshotDTO = new VolumeSnapshotData();
                                VolumeSnapshotInventory volumeSnapshot = (VolumeSnapshotInventory) snapshot;
                                volumeSnapshotDTO.setUuid(volumeSnapshot.getUuid());
                                volumeSnapshotDTO.setName(volumeSnapshot.getName());
                                volumeSnapshotDTO.setDescription(volumeSnapshot.getDescription());
                                volumeSnapshotDTO.setVolumeUuid(volumeSnapshot.getVolumeUuid());
                                volumeSnapshotDTO.setPrimaryStorageUuid(volumeSnapshot.getPrimaryStorageUuid());
                                volumeSnapshotDTO.setInstallPath(volumeSnapshot.getPrimaryStorageInstallPath());
                                volumeSnapshotDTO.setType(volumeSnapshot.getType());
                                volumeSnapshotDTO.setVolumeType(volumeSnapshot.getVolumeType());
                                volumeSnapshotDTO.setLatest(volumeSnapshot.getLatest().toString());
                                volumeSnapshotDTO.setSize(volumeSnapshot.getSize());
                                volumeSnapshotDTO.setStatus(volumeSnapshot.getState());
                                volumeSnapshotDTO.setCreateTime(volumeSnapshot.getCreateDate());
                                volumeSnapshotDTO.setVUpdateDate(volumeSnapshot.getLastOpDate());
                                volumeSnapshotDTO.setFormat(volumeSnapshot.getFormat());
                                //平台信息
                                volumeSnapshotDTO.setType("Storage".equals(volumeSnapshot.getType()) ? "云盘快照" : "主机快照");
                                volumeSnapshotDTO.setPlatformName(platform.getPlatformName());
                                volumeSnapshotDTO.setPlatformId(platform.getPlatformId());
                                volumeSnapshotDTO.setTypeName("zstack");
                                volumeSnapshotDTO.setIsMemory(false);
                                dataList.add(volumeSnapshotDTO);
                            }
                        }

                        if (!dataList.isEmpty()) {
                            message.setType(ClusterMsg.MessageType.BASIC);
                            message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(dataList)
                                    .metricsName(BASIC_SNAPSHOT.code())
                                    .build()));
                            message.setTime(System.currentTimeMillis());
                            sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        }

                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);

                        return dataList;
                    });
                } catch (Exception e) {
                    log.error("平台 {} 快照数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                }
            });
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_SNAPSHOT.code();
    }
}
