package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.MetricsType.BASIC_HOST_VIC_FUSIONONE;

@Slf4j
public class InSpurBasicHostNicDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                List<HostNicData> list = collectData(platform);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST_VIC.code())
                        .metricsType(BASIC_HOST_VIC_FUSIONONE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮物理机网络采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<HostNicData> collectData(Platform platform) {
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String hostUrl = platform.getPlatformUrl() + InSpurApiConstant.GET_HOST_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",platform.getInSpurPlatform().getToken()
        );

        JsonObject hostdata = FsApiCacheService.getJsonObject(hostUrl, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(hostArray)) return null;

        List<HostNicData> hostNicDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    List<HostNicData> hostNicData = collectHostNicInfo(platform, jsonElement,header);
                    if (hostNicData != null) {
                        hostNicDataList.addAll(hostNicData);
                    }
                } catch (Exception e) {
                    log.error("处理浪潮云网络数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return hostNicDataList;
    }

    private List<HostNicData> collectHostNicInfo(Platform platform, JsonElement jsonElement, Map<String, String> header) {
        List<HostNicData>  hostNicDataList = new ArrayList<>();
        String platformUrl = platform.getPlatformUrl();

        JsonObject hostBase = GSON.toJsonTree(jsonElement).getAsJsonObject();
        if (ObjectUtil.isNull(hostBase)) return null;
        String id = getStringFromJson(hostBase, "id");
        //获取物理网卡信息
        String url = platformUrl + InSpurApiConstant.NETWORK_LIST.replace("{id}", id);
        JsonObject hostdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray jsonArray = hostdata.getAsJsonArray("items");

        if (ObjectUtil.isNull(jsonArray)) return null;

        //添加网络信息
        for (Object systemIntF : jsonArray) {
            JsonObject jsonObject = (JsonObject) systemIntF;
            HostNicData nicRespDTO = new HostNicData();
            nicRespDTO.setUuid(getStringFromJson(jsonObject,"id"));
            nicRespDTO.setHardwareUuid(getStringFromJson(hostBase,"id"));
            nicRespDTO.setIpAddresses(getStringFromJson(jsonObject,"hostip"));
//            nicRespDTO.setIpSubnet(getStringFromJson(jsonObject,"name"));
            nicRespDTO.setL2NetworkUuid(getStringFromJson(jsonObject,"id"));
            nicRespDTO.setL2NetworkName(getStringFromJson(jsonObject,"name"));
            nicRespDTO.setNetworkType("上行口");
            nicRespDTO.setState(true);
            nicRespDTO.setMac(getStringFromJson(jsonObject,"macAddress"));
            nicRespDTO.setPlatformId(platform.getPlatformId());
            nicRespDTO.setPlatformName(platform.getPlatformName());
            hostNicDataList.add(nicRespDTO);
        }
        return hostNicDataList;
    }

    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_HOST_VIC.code();
    }
}
