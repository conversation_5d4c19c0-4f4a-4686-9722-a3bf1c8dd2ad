package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.L3NetworkInventory;
import org.zstack.sdk.QueryL3NetworkAction;
import org.zstack.sdk.QueryVmInstanceAction;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_VM_VIC;

@Slf4j
public class ZsTackBasicVmNicDataImpl extends AbstractBasicData {

    protected long startTime;
    private static final String KVM = "KVM";

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform =(Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                try {
                    // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        List<VmNicData> list = new ArrayList<>();
                        String token = platform.getZsTackPlatform().getToken();
                        if (token == null && platform.getAkType() == 0) {
                            log.error("平台 {} token为空", platform.getPlatformName());
                            return null;
                        }

                        //查询三层网络列表
                        QueryL3NetworkAction l3NetworkAction = new QueryL3NetworkAction();
                        if (platform.getAkType() == 0) {
                            l3NetworkAction.sessionId = token;
                        } else {
                            l3NetworkAction.accessKeyId = platform.getUsername();
                            l3NetworkAction.accessKeySecret = platform.getPassword();
                        }
                        QueryL3NetworkAction.Result l3res = l3NetworkAction.call();
                        List<L3NetworkInventory> L3Inventories = l3res.value.inventories;

                        // 查询虚拟机列表
                        QueryVmInstanceAction action = new QueryVmInstanceAction();
                        if (platform.getAkType() == 0) {
                            action.sessionId = token;
                        } else {
                            action.accessKeyId = platform.getUsername();
                            action.accessKeySecret = platform.getPassword();
                        }
                        QueryVmInstanceAction.Result res = action.call();

                        if (res != null && res.value != null && CollUtil.isNotEmpty(res.value.inventories)) {
                            for (Object vm : res.value.inventories) {
                                JsonObject jsonObject = GsonUtil.GSON.toJsonTree(vm).getAsJsonObject();
                                JsonElement hypervisorTypeElement = jsonObject.get("hypervisorType");
                                String hypervisorType = hypervisorTypeElement != null && !hypervisorTypeElement.isJsonNull() ?
                                        hypervisorTypeElement.getAsString() : "";
                                String uuid = getStringFromJson(jsonObject, "uuid", "");
                                if (uuid != null && !uuid.isEmpty() && KVM.equals(hypervisorType)) {
                                    // 弹性ip
                                    JsonArray vmNics = jsonObject.getAsJsonArray("vmNics").getAsJsonArray();
                                    if (!vmNics.isEmpty()) {
                                        for (int i = 0; i < vmNics.size(); i++) {
                                            JsonObject asJsonObject = vmNics.get(i).getAsJsonObject();
                                            VmNicData vmNicData = new VmNicData();
                                            vmNicData.setHostUuid(getStringFromJson(asJsonObject,"vmInstanceUuid"));
                                            vmNicData.setName(getStringFromJson(asJsonObject,"internalName"));
                                            vmNicData.setUuid(getStringFromJson(asJsonObject,"uuid"));
                                            vmNicData.setIp(getStringFromJson(asJsonObject,"ip"));
                                            vmNicData.setIp6("");
                                            vmNicData.setPlatformId(platform.getPlatformId());
                                            vmNicData.setPlatformName(platform.getPlatformName());
                                            vmNicData.setMac(getStringFromJson(asJsonObject,"mac"));
                                            vmNicData.setDriver("virtio");
                                            vmNicData.setInClassicNetwork((byte) 0);
                                            String l3NetworkUuid = getStringFromJson(asJsonObject,"l3NetworkUuid");
                                            vmNicData.setNetworkUuid(l3NetworkUuid);
                                            //获取三层网络名称
                                            for (L3NetworkInventory l3Inventory : L3Inventories) {
                                                if (l3Inventory.getUuid().equals(l3NetworkUuid)) {
                                                    vmNicData.setNetworkName(l3Inventory.getName());
                                                    break;
                                                }
                                            }
                                            list.add(vmNicData);
                                        }
                                    }
                                }
                            }
                        }

                        if (!list.isEmpty()) {
                            BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                                    .metricsName(BASIC_VM_VIC.code())
                                    .build();
                            String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                            log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                            message.setType(ClusterMsg.MessageType.BASIC);
                            message.setData(GsonUtil.GSON.toJson(build));
                            message.setTime(System.currentTimeMillis());
                            sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        }

                        return list;
                    });
                } catch (Exception e) {
                    log.error("平台 {} 虚拟机网卡数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                }
            });
        }
    }


    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_VM_VIC.code();
    }
}
