package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.enums.OtherEnum;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.collect.scanprot.ping.ScanPingData.checkNetworkPing;
import static cn.coder.zj.module.collector.collect.scanprot.snmp.ScanSnmpData.checkSnmpEndpoints;
import static cn.coder.zj.module.collector.collect.scanprot.tcp.ScanTcpData.checkTcpPorts;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_STORAGE;

@Slf4j
public class InSpurBasicStorageDataImpl extends AbstractBasicData {


    private long startTime;


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformInfo : platformList) {
            taskExecutor.execute(() -> {
                List<StorageData> dataList = collectData(platformInfo);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(dataList)
                        .metricsName(BASIC_STORAGE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云存储采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }

    }

    private List<StorageData> collectData(Object platformInfo) {
        Platform platform = (Platform) platformInfo;
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        // 构建请求头
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization", token
        );

        // 获取存储数据
        String platformUrl = platform.getPlatformUrl();
        JsonObject vmdata = FsApiCacheService.getJsonObject(platformUrl + InSpurApiConstant.DATASTORES_LIST, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();

        // 获取主机数据并构建映射
        JsonObject hostdata = FsApiCacheService.getJsonObject(platformUrl + InSpurApiConstant.GET_HOST_LIST, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("items");
        Map<String, JsonObject> hostMap = StreamSupport.stream(hostArray.spliterator(), false)
                .filter(Objects::nonNull)
                .map(JsonElement::getAsJsonObject)
                .collect(Collectors.toMap(
                        host -> getStringFromJson(host, "dataCenterId"),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        List<StorageData> storageList = new ArrayList<>();
        Date currentTime = new Date();

        // 处理存储数据
        for (JsonElement jsonElement : vmArray) {
            JsonObject object = jsonElement.getAsJsonObject();
            JsonObject hostId = hostMap.get(getStringFromJson(object, "dataCenterId"));
            String dataStoreType = getStringFromJson(object, "dataStoreType");

            String label = switch (dataStoreType) {
                case "OCFS" -> "CFS存储池";
                case "NFS" -> "NFS存储池";
                case "LOCAL" -> "本地存储池";
                case "BLOCK_DEVICE" -> "裸磁盘";
                default -> "未知存储类型";
            };

            // 确定可用区
            boolean hasClusterId = hostId.has("dataCenterName") && StrUtil.isNotEmpty(getStringFromJson(hostId, "dataCenterName"));
            String zone = hasClusterId ? getStringFromJson(hostId, "dataCenterName") : getStringFromJson(hostId, "ip");

            // 构建存储数据
            StorageData storageData = StorageData.builder()
                    .name(getStringFromJson(object, "name"))
                    .uuid(getStringFromJson(object, "id"))
                    .url(getStringFromJson(object, "mountPath"))
                    .state("Enabled")
                    .status("Connected")
                    .totalCapacity(getLongFromJsonDouble(object, "capacityInByte"))
                    .usedCapacity(getLongFromJsonDouble(object, "usedCapacityInByte"))
                    .availableCapacity(getBigFromJson(object, "availCapacityInByte"))
                    .capacityUtilization(calculateUsageRate(object))
                    .totalPhysicalCapacity(getBigFromJson(object, "capacityInByte"))
                    .availablePhysicalCapacity(getBigFromJson(object, "availCapacityInByte"))
                    .type(label)
                    .platformId(platform.getPlatformId())
                    .platformName(platform.getPlatformName())
                    .regionId(platform.getRegionId())
                    .deleted(0)
                    .typeName("inspur")
                    .createTime(currentTime)
                    .sCreateTime(currentTime)
                    .vUpdateTime(currentTime)
                    .mediaType("机械盘")
                    .manager(platform.getPlatformName())
                    .availableManager(zone)
                    .storagePercent(BigDecimal.ONE)
                    .remark(getStringFromJson(object, "description"))
                    .virtualCapacity(calculateVirtualCapacity(object))
                    .allocation(getBigFromJson(object, "availCapacityInByte"))
                    .commitRate(commitRate(object))
                    .build();

            String id = getStringFromJson(object, "id");
            JsonObject data = FsApiCacheService.getJsonObject(platformUrl + InSpurApiConstant.GET_STORAGES_LIST.replace("{id}", id), null, header);
            JsonArray array = data.getAsJsonArray("items");

            // 构建关联数据
            List<StorageHostRelationData> relationDataList = new ArrayList<>();
            for (JsonElement element : array) {
                JsonObject bj = element.getAsJsonObject();
                StorageHostRelationData relationData = StorageHostRelationData.builder()
                        .hardwareUuid(getStringFromJson(bj, "id"))
                        .storageUuid(getStringFromJson(object, "id"))
                        .platformId(platform.getPlatformId())
                        .platformName(platform.getPlatformName())
                        .updateTime(currentTime)
                        .build();

                relationDataList.add(relationData);
            }
            storageData.setStorageHostRelationDataList(relationDataList);
            storageList.add(storageData);
        }
        return storageList;
    }

    /**
     * 计算分配容量
     */
    private static BigDecimal commitRate(JsonObject object) {
        BigDecimal capacityInByte = getBigFromJson(object,"capacityInByte");
        BigDecimal availableDecimal = getBigFromJson(object,"availCapacityInByte");
        return availableDecimal.divide(capacityInByte, 2, RoundingMode.HALF_UP);
    }


    /**
     * 计算虚拟容量
     */
    private static BigDecimal calculateVirtualCapacity(JsonObject jsonObject) {
        BigDecimal totalPhysicalCapacity = getBigFromJson(jsonObject,"capacityInByte");
        return totalPhysicalCapacity.multiply(new BigDecimal(1));
    }

    /**
     * 计算使用率
     * @param jsonObject
     * @return
     */
    public static BigDecimal calculateUsageRate(JsonObject jsonObject) {
        BigDecimal totalCapacity = jsonObject.get("capacityInByte").getAsBigDecimal();
        long usedCapacity = jsonObject.get("usedCapacityInByte").getAsLong();
        return (totalCapacity != null && totalCapacity.compareTo(BigDecimal.ZERO) > 0)
                ? BigDecimal.valueOf(usedCapacity).divide(totalCapacity, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO;
    }


    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_STORAGE.code();
    }
}
