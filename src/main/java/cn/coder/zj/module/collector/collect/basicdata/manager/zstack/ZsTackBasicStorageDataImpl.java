package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_STORAGE;
import static java.util.Arrays.asList;


@Slf4j
public class ZsTackBasicStorageDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            try {
                // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                    List<StorageData> dataList = new ArrayList<>();
                    String token = platform.getZsTackPlatform().getToken();
                    if (token == null && platform.getAkType() == 0) {
                        log.error("平台 {} token为空", platform.getPlatformName());
                        return null;
                    }

                    // 主存储总容量
                    GetMetricDataAction action = new GetMetricDataAction();
                    action.namespace = "ZStack/PrimaryStorage";
                    action.metricName = "TotalPhysicalCapacityInBytes";
                    if (platform.getAkType() == 0) {
                        action.sessionId = token;
                    } else {
                        action.accessKeyId = platform.getUsername();
                        action.accessKeySecret = platform.getPassword();
                    }
                    GetMetricDataAction.Result result = action.call();
                    List<Map<String, Object>> totalCap = new ArrayList<>();
                    if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                        JsonArray asJsonArray = GsonUtil.GSON.toJsonTree(result.value.data).getAsJsonArray();
                        List<Map<String, Object>> totalMaps = parseJSONArray(asJsonArray, "totalUsed", "value");
                        totalMaps.stream().collect(Collectors.groupingBy(map -> map.get("PrimaryStorageUuid"))).forEach((k, v) -> {
                            Map<String, Object> map = new HashMap();
                            map.put("uuid", k);
                            map.put("totalUsed", v.get(v.size() - 1).get("totalUsed"));
                            totalCap.add(map);
                        });
                    }

                    QueryHostAction actionHost = new QueryHostAction();
                    if (platform.getAkType() == 0) {
                        actionHost.sessionId = token;
                    } else {
                        actionHost.accessKeyId = platform.getUsername();
                        actionHost.accessKeySecret = platform.getPassword();
                    }
                    QueryHostAction.Result resHost = actionHost.call();
                    List<KVMHostInventory> hostActionList = resHost.value.inventories;

                    QueryPrimaryStorageAction storageAction = new QueryPrimaryStorageAction();
                    if (platform.getAkType() == 0) {
                        storageAction.sessionId = token;
                    } else {
                        storageAction.accessKeyId = platform.getUsername();
                        storageAction.accessKeySecret = platform.getPassword();
                    }
                    QueryPrimaryStorageAction.Result res = storageAction.call();
                    if (res != null && res.value != null && CollUtil.isNotEmpty(res.value.inventories)) {
                        JsonArray asJsonArray = GsonUtil.GSON.toJsonTree(res.value.inventories).getAsJsonArray();
                        asJsonArray.forEach(jsonElement -> {
                            StorageData storageData = new StorageData();
                            JsonObject jsonObject = jsonElement.getAsJsonObject();
                            String name = jsonObject.get("name").getAsString();
                            JsonArray attachedClusters = jsonObject.getAsJsonArray("attachedClusterUuids");
                            String clusterUuids = CollUtil.isNotEmpty(attachedClusters) ? attachedClusters.get(0).getAsString() : "";
                            AtomicReference<String> clusterName = new AtomicReference<>("");
                            QueryClusterAction clusters = new QueryClusterAction();
                            clusters.conditions = asList("hypervisorType=KVM");
                            if (platform.getAkType() == 0) {
                                clusters.sessionId = token;
                            } else {
                                clusters.accessKeyId = platform.getUsername();
                                clusters.accessKeySecret = platform.getPassword();
                            }
                            clusters.call().value.inventories.forEach(cluster -> {
                                JsonObject clusterObj = GsonUtil.GSON.toJsonTree(cluster).getAsJsonObject();
                                if (clusterUuids.equals(clusterObj.get("uuid").getAsString())) {
                                    clusterName.set(clusterObj.get("name").getAsString());
                                }
                            });
                            String uuid = jsonObject.get("uuid").getAsString();
                            String url = jsonObject.get("url").getAsString();
                            String state = jsonObject.get("state").getAsString();
                            String tyep = jsonObject.get("type").getAsString();
                            String status = jsonObject.get("status").getAsString();
                            BigDecimal availablePhysicalCapacity = jsonObject.get("availablePhysicalCapacity").getAsBigDecimal();
                            BigDecimal totalPhysicalCapacity = jsonObject.get("totalPhysicalCapacity").getAsBigDecimal();
                            BigDecimal availableCapacity = jsonObject.get("availableCapacity").getAsBigDecimal();
                            BigDecimal capacityUtilization = new BigDecimal(0);
                            Date date = DateUtil.date(new Date(jsonObject.get("createDate").getAsString()));
                            Long totalCapacity = 0L;
                            Long usedCapacity = 0L;
                            //查询区域列表
                            QueryZoneAction queryZoneAction = new QueryZoneAction();
                            if (platform.getAkType() == 0) {
                                queryZoneAction.sessionId = token;
                            } else {
                                queryZoneAction.accessKeyId = platform.getUsername();
                                queryZoneAction.accessKeySecret = platform.getPassword();
                            }
                            QueryZoneAction.Result qz = queryZoneAction.call();
                            List regionList = qz.value.inventories;

                            //区域
                            String zoneUuid = jsonObject.get("zoneUuid").getAsString();
                            storageData.setManager("zstack");
                            storageData.setAvailableManager("-");
                            for (Object item : regionList) {
                                if (item instanceof org.zstack.sdk.ZoneInventory) {
                                    org.zstack.sdk.ZoneInventory zone = (org.zstack.sdk.ZoneInventory) item;
                                    if (zone.getUuid().equals(zoneUuid)) {
                                        storageData.setAvailableManager(zone.getName());
                                        break;
                                    }
                                }
                            }

                            storageData.setMediaType("机械盘");
                            storageData.setStoragePercent(new BigDecimal(1));
                            BigDecimal availableDecimal = availableCapacity.compareTo(new BigDecimal(0)) > 0 ? availableCapacity : new BigDecimal(0);
                            //虚拟容量
                            storageData.setVirtualCapacity(jsonObject.get("totalPhysicalCapacity").getAsBigDecimal().multiply(storageData.getStoragePercent()));
                            storageData.setAllocation(jsonObject.get("totalPhysicalCapacity").getAsBigDecimal().subtract(availableDecimal));
                            String total = getStringFromJson(jsonObject, "totalPhysicalCapacity");
                            if (total.equals("0") || total.equals("")) {
                                storageData.setCommitRate(new BigDecimal(0));
                            } else {
                                storageData.setCommitRate(storageData.getAllocation().divide(jsonObject.get("totalPhysicalCapacity").getAsBigDecimal(), 2, RoundingMode.HALF_UP));
                            }
                            storageData.setRemark("-");
                            storageData.setVUpdateTime(date);
                            //存储容量使用率
                            GetMetricDataAction in = new GetMetricDataAction();
                            in.namespace = "ZStack/PrimaryStorage";
                            in.metricName = "UsedCapacityInPercent";
                            if (platform.getAkType() == 0) {
                                in.sessionId = token;
                            } else {
                                in.accessKeyId = platform.getUsername();
                                in.accessKeySecret = platform.getPassword();
                            }
                            in.labels = asList("PrimaryStorageUuid=" + uuid);
                            GetMetricDataAction.Result prResult = in.call();
                            if (prResult != null && prResult.value != null && CollUtil.isNotEmpty(prResult.value.data)) {
                                JsonObject dataObj = GsonUtil.GSON.toJsonTree(prResult.value.data.get(0)).getAsJsonObject();
                                capacityUtilization = dataObj.get("value").getAsBigDecimal();
                            }

                            // 已用容量
                            GetMetricDataAction out = new GetMetricDataAction();
                            out.namespace = "ZStack/PrimaryStorage";
                            out.metricName = "UsedCapacityInBytes";
                            if (platform.getAkType() == 0) {
                                out.sessionId = token;
                            } else {
                                out.accessKeyId = platform.getUsername();
                                out.accessKeySecret = platform.getPassword();
                            }
                            out.labels = asList("PrimaryStorageUuid=" + uuid);
                            GetMetricDataAction.Result outResult = out.call();
                            if (outResult != null && outResult.value != null && CollUtil.isNotEmpty(outResult.value.data)) {
                                JsonObject dataObj = GsonUtil.GSON.toJsonTree(outResult.value.data.get(0)).getAsJsonObject();
                                usedCapacity = dataObj.get("value").getAsLong();
                            }
                            for (Map map : totalCap) {
                                if (StringUtil.toString(map.get("uuid")).equals(uuid)) {
                                    totalCapacity = ((BigDecimal) map.get("totalUsed")).longValue();
                                }
                            }

                            JsonArray attachedClusterUuids = jsonObject.getAsJsonArray("attachedClusterUuids");
                            List<StorageHostRelationData> storageHostRelationDataList = new ArrayList<>();
                            Map<String, List<KVMHostInventory>> clusterToHostsMap = hostActionList.stream().filter(item -> item.clusterUuid != null).collect(Collectors.groupingBy(item -> item.clusterUuid));
                            for (JsonElement mon : attachedClusterUuids) {
                                String clusterUuid = mon.getAsString();
                                List<KVMHostInventory> matchedHosts = clusterToHostsMap.getOrDefault(clusterUuid, Collections.emptyList());
                                for (KVMHostInventory host : matchedHosts) {
                                    StorageHostRelationData relation = new StorageHostRelationData();
                                    relation.setPlatformId(platform.getPlatformId());
                                    relation.setPlatformName(platform.getPlatformName());
                                    relation.setStorageUuid(uuid);
                                    relation.setHardwareUuid(host.getUuid());
                                    storageHostRelationDataList.add(relation);
                                }
                            }

                            storageData.setStorageHostRelationDataList(storageHostRelationDataList);
                            storageData.setDeleted(0);
                            storageData.setUuid(uuid);
                            storageData.setName(name);
                            storageData.setUrl(url);
                            storageData.setState(state);
                            storageData.setType(tyep);
                            storageData.setStatus(status);
                            storageData.setCapacityUtilization(capacityUtilization.compareTo(new BigDecimal(0)) > 0 ? (capacityUtilization.compareTo(new BigDecimal(0)) > 100 ? new BigDecimal(100) : capacityUtilization) : new BigDecimal(0));
                            storageData.setUsedCapacity(usedCapacity);
                            storageData.setTotalCapacity(totalCapacity);
                            storageData.setAvailablePhysicalCapacity(availablePhysicalCapacity.compareTo(new BigDecimal(0)) > 0 ? availablePhysicalCapacity : new BigDecimal(0));
                            storageData.setAvailableCapacity(availableDecimal);
                            storageData.setTotalPhysicalCapacity(totalPhysicalCapacity.compareTo(new BigDecimal(0)) > 0 ? totalPhysicalCapacity : new BigDecimal(0));

                            storageData.setPlatformId(platform.getPlatformId());
                            storageData.setPlatformName(platform.getPlatformName());
                            storageData.setTypeName("zstack");
                            storageData.setClusterUuid(clusterUuids);
                            storageData.setClusterName(clusterName.toString());
                            storageData.setCreateTime(date);
                            storageData.setSCreateTime(date);
                            dataList.add(storageData);
                        });
                    }

                    if (!dataList.isEmpty()) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(dataList)
                                .metricsName(BASIC_STORAGE.code())
                                .build();
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    }

                    return dataList;
                });
            } catch (Exception e) {
                log.error("平台 {} 存储数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
            }

        }
    }


    private List<Map<String, Object>> parseJSONArray(JsonArray jsonArray, String labelKey, String valueKey) {
        List<Map<String, Object>> maps = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JsonObject jsonObject = jsonArray.get(i).getAsJsonObject();
            Map<String, Object> map = new HashMap<>();
            if ("Ceph".equals(jsonObject.get("labels").getAsJsonObject().get("PrimaryStorageType").getAsString())) {
                map.put("time", jsonObject.get("time").getAsLong());
                map.put("type", jsonObject.getAsJsonObject("labels").get("PrimaryStorageType").getAsString());
                map.put("PrimaryStorageUuid", jsonObject.getAsJsonObject("labels").get("PrimaryStorageUuid").getAsString());
                map.put(labelKey, jsonObject.get(valueKey).getAsBigDecimal());
                maps.add(map);
            }
        }
        return maps;
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_STORAGE.code();
    }
}
