package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_VOLUME_INFO;

@Slf4j
public class InSpurBasicVolumeInfoDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeInfoData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VOLUME_INFO.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云云盘采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeInfoData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        // 构建请求头
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization", platform.getInSpurPlatform().getToken()
        );

        // 获取卷数据
        String platformUrl = platform.getPlatformUrl();
        JsonObject vmdata = FsApiCacheService.getJsonObject(platformUrl + InSpurApiConstant.GET_VOLUMES, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();

        // 获取云主机数据并构建映射
        JsonObject hostdata = FsApiCacheService.getJsonObject(platformUrl + InSpurApiConstant.GET_CLOUD_LIST, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("items");
        Map<String, JsonObject> hostMap = StreamSupport.stream(hostArray.spliterator(), false)
                .filter(Objects::nonNull)
                .map(JsonElement::getAsJsonObject)
                .collect(Collectors.toMap(
                        host -> getStringFromJson(host, "name"),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        // 处理卷数据
        List<VolumeInfoData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    VolumeInfoData vmData = collectVmInfoData(platform, jsonElement, hostMap,header);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理浪潮云云盘数据异常: {}", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private VolumeInfoData collectVmInfoData(Platform platform, JsonElement jsonElement, Map<String, JsonObject> hostMap,Map<String, String> header) {
        JsonObject volume = jsonElement.getAsJsonObject();
        String vmName = getStringFromJson(volume, "vmName");
        JsonObject hostId = hostMap.get(vmName);

        // 创建并填充卷信息对象
        VolumeInfoData volumeDTO = new VolumeInfoData();
        volumeDTO.setDescription(getStringFromJson(volume, "description"));
        volumeDTO.setName(getStringFromJson(volume, "name"));
        volumeDTO.setFormat(getStringFromJson(volume, "format","raw").toLowerCase());
        volumeDTO.setType(getBooleanFromJson(volume, "bootable") ? "Root" : "Data");
        volumeDTO.setState("Enabled");
        volumeDTO.setStatus("Ready");
        volumeDTO.setUuid(getStringFromJson(volume, "id"));
        volumeDTO.setMediaType("rotate");

        // 设置关联信息
        long sizeInByte = getLongFromJsonDouble(volume, "sizeInByte");
        long realSizeInByte = getLongFromJsonDouble(volume, "realSizeInByte");
        if (hostId != null) {
            String hostIdStr = getStringFromJson(hostId, "id");

            String url = platform.getPlatformUrl() + InSpurApiConstant.GET_CLOUD_INFO;
            String replacedUrl = url.replace("{id}", hostIdStr);
            JsonObject vmdata = FsApiCacheService.getJsonObject(replacedUrl, null, header);
            JsonArray disks = vmdata.getAsJsonArray("disks");

            String currentVolumeName = getStringFromJson(volume, "name");
            for (JsonElement diskElement : disks) {
                JsonObject volumeInfo = diskElement.getAsJsonObject().getAsJsonObject("volume");
                if (currentVolumeName.equals(getStringFromJson(volumeInfo, "name"))) {
                    sizeInByte = getLongFromJsonDouble(volumeInfo, "sizeInByte");
                    realSizeInByte = getLongFromJsonDouble(volumeInfo, "realSizeInByte");
                    break;
                }
            }
            volumeDTO.setVmInstanceUuid(hostIdStr);
            volumeDTO.setVmInstanceName(vmName);
            volumeDTO.setIsMount(true);
        } else {
            volumeDTO.setVmInstanceUuid("");
            volumeDTO.setVmInstanceName("");
            volumeDTO.setIsMount(false);
        }

        volumeDTO.setPrimaryStorageUuid(getStringFromJson(volume, "dataStoreId"));
        volumeDTO.setPrimaryStorageName(getStringFromJson(volume, "dataStoreName"));
        volumeDTO.setPrimaryStorageType(getStringFromJson(volume, "dataStoreType").toLowerCase());

        // 设置容量信息
        volumeDTO.setSize(sizeInByte);
        volumeDTO.setActualSize(realSizeInByte);
        volumeDTO.setActualFree(sizeInByte - realSizeInByte);
        volumeDTO.setActualUse(realSizeInByte);
        volumeDTO.setActualRatio(calculateRatio(realSizeInByte, sizeInByte));

        // 设置平台信息
        volumeDTO.setPlatformId(String.valueOf(platform.getPlatformId()));
        volumeDTO.setPlatformName(platform.getPlatformName());

        // 设置创建时间
        if (hostId != null) {
            String createTime = getStringFromJson(hostId, "createTime");
            try {
                volumeDTO.setVCreateDate(StrUtil.isNumeric(createTime) ?
                        DateUtil.date(Long.parseLong(createTime) * 1000L) :
                        DateUtil.parseDateTime(createTime));
            } catch (Exception e) {
                volumeDTO.setVCreateDate(DateUtil.date());
            }
        } else {
            volumeDTO.setVCreateDate(DateUtil.date());
        }

        return volumeDTO;
    }

    private String calculateRatio(Long used, Long total) {
        if (total == 0) return "0";
        return new BigDecimal(used)
                .multiply(new BigDecimal(100))
                .divide(new BigDecimal(total), 2, BigDecimal.ROUND_HALF_UP)
                .toString();
    }

    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_VOLUME_INFO.code();
    }
}
