package cn.coder.zj.module.collector.constants.inspur;

public class InSpurApiConstant {
    
    public static final String LOGIN = "/system/user/login";

    //----------------虚拟机------------------------
    public static final String GET_CLOUD_LIST = "/vms";

    public static final String GET_CLOUD_INFO = "/vms/{id}";

    public static final String GET_VOLUMES = "/volumes";

    public static final String GET_IMAGE_LIST = "/vmtemplates";

    public static final String VM_NETWORK_LIST = "/networks";

    //----------------物理机------------------------
    public static final String GET_HOST_LIST = "/hosts";

    public static final String GET_STORAGES_LIST = "/storages/{id}/hosts";

    public static final String DATASTORES_LIST = "/storages";

    public static final String NETWORK_LIST = "/hosts/{id}/pnicsFromDB";

    public static final String GET_REALTIME_DATA = "/perfcharts/charts";

}


