package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostSecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import cn.iocoder.zj.framework.common.dal.manager.aggregation.SecGroupAggData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SEC_GROUP;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_SEC_GROUP;
import static java.util.Arrays.asList;

/**
 * <AUTHOR>
 **/
@Slf4j
public class ZsTackBasicSecGroupDataImpl extends AbstractBasicData {

    protected long startTime;

    private static final String KVM = "KVM";

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }

            try {
                // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                    List<SecGroupAggData> secGroupData = collectData(platform);

                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(secGroupData).metricsName(BASIC_SEC_GROUP.code()).build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());

                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);

                    return secGroupData;
                });
            } catch (Exception e) {
                log.error("平台 {} 安全组数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
            }

        }

    }

    private List<SecGroupAggData> collectData(Platform platform) {
        List<SecGroupAggData> aggData = Lists.newArrayList();
        //组装对象
        SecGroupAggData groupInfos = new SecGroupAggData();
        List<SecGroupData> secGroups = Lists.newArrayList();
        List<SecGroupRuleData> secGroupRules = Lists.newArrayList();
        List<HostSecGroupData> hostSecGroups = Lists.newArrayList();
        String token = platform.getZsTackPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return aggData;
        }
        //查询数据
        QuerySecurityGroupAction groupAction = new QuerySecurityGroupAction();
        if (platform.getAkType() == 0) {
            groupAction.sessionId = token;
        } else {
            groupAction.accessKeyId = platform.getUsername();
            groupAction.accessKeySecret = platform.getPassword();
        }
        QuerySecurityGroupAction.Result groupRes = groupAction.call();
        List<SecurityGroupInventory> groupInventories = groupRes.value.inventories;
        Map<String, SecurityGroupInventory> groupMap = groupInventories == null ? new HashMap<>() : groupInventories.stream().collect(Collectors.toMap(SecurityGroupInventory::getUuid, Function.identity()));
        for (SecurityGroupInventory item : groupInventories) {
            SecGroupData createReqDto = new SecGroupData();
            createReqDto.setUuid(item.getUuid());
            createReqDto.setName(item.getName());
            createReqDto.setStatus(item.getState());
            createReqDto.setDescription(item.getDescription());
            createReqDto.setPlatformId(platform.getPlatformId());
            createReqDto.setPlatformName(platform.getPlatformName());
            //添加安全组数据
            secGroups.add(createReqDto);
            List<SecurityGroupRuleInventory> rules = item.getRules();
            for (SecurityGroupRuleInventory rule : rules) {
                SecGroupRuleData secGroupRule = new SecGroupRuleData();
                secGroupRule.setUuid(rule.getUuid());
                secGroupRule.setSecgroupUuid(rule.getSecurityGroupUuid());
                secGroupRule.setStatus(rule.getState());
                secGroupRule.setDescription(rule.getDescription());
                secGroupRule.setCidr(rule.getAllowedCidr());
                secGroupRule.setProtocol(rule.getProtocol());
                String dstPortRange = rule.getDstPortRange();
                secGroupRule.setPriority(rule.getPriority());
                secGroupRule.setDirection(Objects.equals(rule.getType(), "Ingress") ? "in" : "out");
                if (StrUtil.isNotEmpty(dstPortRange)) {
                    secGroupRule.setPorts(dstPortRange);
                } else {
                    secGroupRule.setPorts("任意端口");
                }
                secGroupRule.setPlatformId(platform.getPlatformId());
                secGroupRule.setPlatformName(platform.getPlatformName());
                //安全组规则数据
                secGroupRules.add(secGroupRule);
            }
        }
        QueryVmInstanceAction action = new QueryVmInstanceAction();
        if (platform.getAkType() == 0) {
            action.sessionId = token;
        } else {
            action.accessKeyId = platform.getUsername();
            action.accessKeySecret = platform.getPassword();
        }
        QueryVmInstanceAction.Result res = action.call();

        //关联关系
        QueryVmNicInSecurityGroupAction actionVm = new QueryVmNicInSecurityGroupAction();
        actionVm.conditions = asList();
        if (platform.getAkType() == 0) {
            actionVm.sessionId = token;
        } else {
            actionVm.accessKeyId = platform.getUsername();
            actionVm.accessKeySecret = platform.getPassword();
        }
        QueryVmNicInSecurityGroupAction.Result resVm = actionVm.call();

        if (res != null && res.value != null && CollUtil.isNotEmpty(res.value.inventories)) {
            for (Object vm : res.value.inventories) {
                JsonObject jsonObject = GsonUtil.GSON.toJsonTree(vm).getAsJsonObject();
                JsonElement hypervisorTypeElement = jsonObject.get("hypervisorType");
                String hypervisorType = hypervisorTypeElement != null && !hypervisorTypeElement.isJsonNull() ? hypervisorTypeElement.getAsString() : "";
                String uuid = getStringFromJson(jsonObject, "uuid", "");
                if (uuid != null && !uuid.isEmpty() && KVM.equals(hypervisorType)) {
                    List<SecurityGroupInventory> list = new ArrayList<>();
                    if (resVm != null && resVm.value != null && CollUtil.isNotEmpty(resVm.value.inventories)) {
                        for (Object inventory : resVm.value.inventories) {
                            JsonObject group = GsonUtil.GSON.toJsonTree(inventory).getAsJsonObject();
                            String vmInstanceUuid = getStringFromJson(group, "vmInstanceUuid", "");
                            if (vmInstanceUuid.equals(uuid)) {
                                String securityGroupUuid = getStringFromJson(group, "securityGroupUuid", "");
                                SecurityGroupInventory securityGroupInventory = groupMap.get(securityGroupUuid);
                                list.add(securityGroupInventory);
                            }
                        }
                    }

                    for (SecurityGroupInventory item : list) {
                        HostSecGroupData hostSecGroup = new HostSecGroupData();
                        hostSecGroup.setPlatformId(platform.getPlatformId());
                        hostSecGroup.setPlatformName(platform.getPlatformName());
                        hostSecGroup.setHostUuid(uuid);
                        hostSecGroup.setSecgroupUuid(item.getUuid());
                        hostSecGroups.add(hostSecGroup);
                    }
                }
            }
        }
        //添加到集合里
        groupInfos.setSecGroups(secGroups);
        groupInfos.setSecGroupRules(secGroupRules);
        groupInfos.setHostSecGroups(hostSecGroups);
        aggData.add(groupInfos);

        return aggData;
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_SEC_GROUP.code();
    }
}
