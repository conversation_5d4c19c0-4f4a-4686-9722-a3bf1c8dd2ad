package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_ZS_TACK_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static java.util.Arrays.asList;


@Slf4j
public class ZsTackCpuImpl extends AbstractMetrics {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                try {
                    Platform platform = (Platform) o;
                    // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        List<MetricData> metricDataList = new ArrayList<>();
                        String token = platform.getZsTackPlatform().getToken();
                        if (token == null && platform.getAkType() == 0) {
                            log.error("平台 {} token为空", platform.getPlatformName());
                            return null;
                        }
                        // 获取所有VM的UUID并组合成标签
                        List<MonitorInfo> vmUuids = platform.getZsTackPlatform().getVmUuids();
                        List<MonitorInfo> hostUuids = platform.getZsTackPlatform().getHostUuids();
                        if (vmUuids == null || vmUuids.isEmpty()) {
                            log.error("平台 {} VM UUID为空", platform.getPlatformName());
                            return null;
                        }

                        metricDataList.addAll(vmUUid(vmUuids, token, platform));
                        metricDataList.addAll(hostUUid(hostUuids, token, platform));

                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.CPU_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect collectData data end, cost {} seconds 平台 {}", endTimeFormatted ,platform.getPlatformName());

                        return metricDataList;
                    });
                } catch (Exception e) {
                    log.error("平台 {} CPU指标收集失败: {}", ((Platform) o).getPlatformName(), e.getMessage(), e);
                }
            });
        }

    }


    public List<MetricData> vmUUid(List<MonitorInfo> vmUuids, String token, Platform platform) {
        return getMetricData(vmUuids, token, "ZStack/VM", "VMUuid=", platform,"vm");
    }

    public List<MetricData> hostUUid(List<MonitorInfo> hostUuids, String token, Platform platform) {
        return getMetricData(hostUuids, token, "ZStack/Host", "HostUuid=", platform,"host");
    }

    private List<MetricData> getMetricData(List<MonitorInfo> uuids, String token, String namespace, String labelPrefix, Platform platform,String type) {
        List<MetricData> metricDataList = new ArrayList<>();
        for (MonitorInfo monitorInfo : uuids) {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = "CPUAverageUsedUtilization";
            action.labels = asList(labelPrefix.concat(monitorInfo.getUuid()));
            if (platform.getAkType()==0){
                action.sessionId = token;
            }else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }

            action.period = 20;
            action.startTime = System.currentTimeMillis() / 1000 - 60;  // 60秒前
            action.endTime = System.currentTimeMillis() / 1000;         // 当前时间

            MetricData metricData = new MetricData();
            metricData.setResourceId(monitorInfo.getUuid());
            metricData.setResourceName(monitorInfo.getName());
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setType(type);
            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            GetMetricDataAction.Result result = action.call();
            if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                timestamps.add(dataObj.get("time").getAsLong());
                values.add(dataObj.get("value").getAsDouble());
                metricData.setMetricName(CPU_USED_TASK.code());
                metricData.setTimestamps(timestamps);
                metricData.setValues(values);
                metricDataList.add(metricData);
            }
        }
        return metricDataList;
    }


    @Override
    public String supportProtocol() {
        return PROTOCOL_ZS_TACK_CPU.code();
    }
}
