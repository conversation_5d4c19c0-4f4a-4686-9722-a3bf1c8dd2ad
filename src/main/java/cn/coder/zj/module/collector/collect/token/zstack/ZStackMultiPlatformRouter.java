package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.zstack.sdk.ZSClient;
import org.zstack.sdk.ZSConfig;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * <p>
 * ZStack多平台路由器
 * <p>
 * 解决ZStack SDK全局配置冲突问题，支持多平台并发访问
 * <p>
 * 核心功能：
 * 1. 平台级别的配置隔离
 * 2. 智能配置切换和缓存
 * 3. 线程安全的并发控制
 * 4. 最小化配置切换开销
 *
 * <AUTHOR>
 */
@Slf4j
public class ZStackMultiPlatformRouter {

    // 平台配置缓存
    private static final ConcurrentHashMap<String, PlatformConfig> platformConfigs = new ConcurrentHashMap<>();

    // 平台级别的锁 - 每个平台独立锁，避免全局阻塞
    private static final ConcurrentHashMap<String, ReentrantLock> platformLocks = new ConcurrentHashMap<>();

    // 全局锁，仅用于ZSClient配置切换的关键时刻
    private static final ReentrantLock globalConfigLock = new ReentrantLock();

    // 当前活跃平台ID - 用于判断是否需要重新配置
    private static volatile String currentActivePlatformId = null;

    // 配置切换统计
    private static volatile long configSwitchCount = 0;

    /**
     * 在指定平台配置下执行操作
     * <p>
     * 这是核心方法，确保在正确的平台配置下执行ZStack SDK操作
     *
     * @param platform  平台信息
     * @param operation 要执行的操作
     * @param <T>       返回值类型
     * @return 操作结果
     * @throws RuntimeException 当平台参数无效或操作执行失败时
     */
    public static <T> T executeWithPlatform(Platform platform, Supplier<T> operation) {

        configureZSClient(platform);
        T result = operation.get();

        return result;

    }

    /**
     * 检查是否需要重新配置ZSClient
     *
     * @param platform 目标平台
     * @return true如果需要重新配置
     */
    private static boolean needReconfigure(Platform platform) {
        String platformId = platform.getPlatformId().toString();

        // 如果当前活跃平台不是目标平台，需要重新配置
        if (!platformId.equals(currentActivePlatformId)) {
            return true;
        }

        // 检查配置是否已缓存且有效
        PlatformConfig cachedConfig = platformConfigs.get(platformId);
        if (cachedConfig == null) {
            return true;
        }

        // 检查配置是否变更（URL或端口变化）
        String currentUrl = platform.getPlatformUrl();
        return !cachedConfig.getUrl().equals(currentUrl);
    }

    /**
     * 配置ZSClient为指定平台
     *
     * @param platform 平台信息
     */
    private static void configureZSClient(Platform platform) {
        String platformId = platform.getPlatformId().toString();
        String platformName = platform.getPlatformName();
        String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
        String port = extractPort(platform.getPlatformUrl());

        log.info("正在为平台 {} 配置ZSClient: {}:{}", platformName, processedUrl, port);

        try {
            // 配置ZSClient
            ZSClient.configure(new ZSConfig.Builder()
                    .setHostname(processedUrl)
                    .setPort(Convert.toInt(port))
                    .setContextPath("zstack")
                    .build());

            ZSConfig config = ZSClient.getConfig();

            // 更新配置缓存
            platformConfigs.put(platformId, new PlatformConfig(platform.getPlatformUrl()));

            log.debug("平台 {} ZSClient配置完成", platformName);

        } catch (Exception e) {
            log.error("平台 {} ZSClient配置失败: {}", platformName, e.getMessage(), e);
            throw new RuntimeException("ZSClient配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 移除URL中的协议和端口部分
     *
     * @param url 原始URL
     * @return 处理后的主机名
     */
    private static String removeProtocolAndPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }

        url = url.trim();

        // 移除协议部分
        if (url.startsWith("http://")) {
            url = url.substring(7);
        } else if (url.startsWith("https://")) {
            url = url.substring(8);
        }

        // 移除端口部分
        int colonIndex = url.indexOf(':');
        if (colonIndex > 0) {
            url = url.substring(0, colonIndex);
        }

        // 移除路径部分
        int slashIndex = url.indexOf('/');
        if (slashIndex > 0) {
            url = url.substring(0, slashIndex);
        }

        return url;
    }

    /**
     * 提取URL中的端口号
     *
     * @param url 原始URL
     * @return 端口号字符串
     */
    private static String extractPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "8080"; // 默认端口
        }

        url = url.trim();

        // 移除协议部分
        if (url.startsWith("http://")) {
            url = url.substring(7);
        } else if (url.startsWith("https://")) {
            url = url.substring(8);
        }

        // 提取端口部分
        int colonIndex = url.indexOf(':');
        if (colonIndex > 0) {
            int slashIndex = url.indexOf('/', colonIndex);
            if (slashIndex > 0) {
                return url.substring(colonIndex + 1, slashIndex);
            } else {
                return url.substring(colonIndex + 1);
            }
        }

        return "8080"; // 默认端口
    }


    /**
     * 清理指定平台的配置缓存
     *
     * @param platform 要清理的平台
     */
    public static void clearPlatformConfig(Platform platform) {
        if (platform != null && platform.getPlatformId() != null) {
            String platformId = platform.getPlatformId().toString();
            platformConfigs.remove(platformId);
            platformLocks.remove(platformId);

            if (platformId.equals(currentActivePlatformId)) {
                currentActivePlatformId = null;
            }

            log.info("已清理平台 {} 的配置缓存", platform.getPlatformName());
        }
    }

    /**
     * 清理所有平台配置缓存
     */
    public static void clearAllPlatformConfigs() {
        platformConfigs.clear();
        platformLocks.clear();
        currentActivePlatformId = null;
        configSwitchCount = 0;
        log.info("已清理所有平台配置缓存");
    }

    /**
     * 平台配置缓存类
     */
    private static class PlatformConfig {
        private final String url;
        private final long createTime;

        public PlatformConfig(String url) {
            this.url = url;
            this.createTime = System.currentTimeMillis();
        }

        public String getUrl() {
            return url;
        }

        public long getCreateTime() {
            return createTime;
        }
    }
}
