package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.zstack.sdk.ZSClient;
import org.zstack.sdk.ZSConfig;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * {{RIPER-5:
 * Action: "Added"
 * Task_ID: "ZStack多平台路由器实现"
 * Timestamp: "2025-07-31T10:00:00Z"
 * Authoring_Role: "LD"
 * Principle_Applied: "SOLID-S (单一职责原则) - 专门负责多平台配置路由"
 * Quality_Check: "线程安全设计，配置缓存优化，完整异常处理"
 * }}
 * <p>
 * ZStack多平台路由器
 * <p>
 * 解决ZStack SDK全局配置冲突问题，支持多平台并发访问
 * <p>
 * 核心功能：
 * 1. 平台级别的配置隔离
 * 2. 智能配置切换和缓存
 * 3. 线程安全的并发控制
 * 4. 最小化配置切换开销
 *
 * <AUTHOR>
 */
@Slf4j
public class ZStackMultiPlatformRouter {

    // 平台配置缓存
    private static final ConcurrentHashMap<String, PlatformConfig> platformConfigs = new ConcurrentHashMap<>();

    // 平台级别的锁 - 每个平台独立锁，避免全局阻塞
    private static final ConcurrentHashMap<String, ReentrantLock> platformLocks = new ConcurrentHashMap<>();

    // 全局锁，仅用于ZSClient配置切换的关键时刻
    private static final ReentrantLock globalConfigLock = new ReentrantLock();

    // 当前活跃平台ID - 用于判断是否需要重新配置
    private static volatile String currentActivePlatformId = null;

    // 配置切换统计
    private static volatile long configSwitchCount = 0;

    /**
     * 在指定平台配置下执行操作
     * <p>
     * 这是核心方法，确保在正确的平台配置下执行ZStack SDK操作
     *
     * @param platform  平台信息
     * @param operation 要执行的操作
     * @param <T>       返回值类型
     * @return 操作结果
     * @throws RuntimeException 当平台参数无效或操作执行失败时
     */
    public static <T> T executeWithPlatform(Platform platform, Supplier<T> operation) {
        if (platform == null) {
            throw new IllegalArgumentException("平台参数不能为空");
        }

        if (platform.getPlatformId() == null) {
            throw new IllegalArgumentException("平台ID不能为空");
        }

        String platformId = platform.getPlatformId().toString();
        String platformName = platform.getPlatformName();

        // 获取或创建平台专用锁
        ReentrantLock platformLock = platformLocks.computeIfAbsent(platformId, k -> new ReentrantLock());

        // 获取平台锁 - 确保同一平台的操作串行执行
        platformLock.lock();
        try {
            log.debug("开始为平台 {} 执行操作", platformName);

//            // 检查是否需要切换配置
//            boolean needReconfigure = needReconfigure(platform);


            // 需要配置切换时，获取全局锁
            globalConfigLock.lock();
            try {
                // 双重检查，避免重复配置
                if (needReconfigure(platform)) {
                    configureZSClient(platform);
                    currentActivePlatformId = platformId;
                    configSwitchCount++;
                    log.debug("已为平台 {} 切换ZSClient配置 (总切换次数: {})", platformName, configSwitchCount);
                }
            } finally {
                globalConfigLock.unlock();
            }


            // 执行实际操作
            long startTime = System.currentTimeMillis();
            T result = operation.get();
            long duration = System.currentTimeMillis() - startTime;

            log.debug("平台 {} 操作完成，耗时: {}ms", platformName, duration);
            return result;

        } catch (Exception e) {
            log.error("平台 {} 操作执行失败: {}", platformName, e.getMessage(), e);
            throw new RuntimeException("ZStack多平台操作执行失败: " + e.getMessage(), e);
        } finally {
            platformLock.unlock();
        }
    }

    /**
     * 检查是否需要重新配置ZSClient
     *
     * @param platform 目标平台
     * @return true如果需要重新配置
     */
    private static boolean needReconfigure(Platform platform) {
        String platformId = platform.getPlatformId().toString();

        // 如果当前活跃平台不是目标平台，需要重新配置
        if (!platformId.equals(currentActivePlatformId)) {
            return true;
        }

        // 检查配置是否已缓存且有效
        PlatformConfig cachedConfig = platformConfigs.get(platformId);
        if (cachedConfig == null) {
            return true;
        }

        // 检查配置是否变更（URL或端口变化）
        String currentUrl = platform.getPlatformUrl();
        return !cachedConfig.getUrl().equals(currentUrl);
    }

    /**
     * 配置ZSClient为指定平台
     *
     * @param platform 平台信息
     */
    private static void configureZSClient(Platform platform) {
        String platformId = platform.getPlatformId().toString();
        String platformName = platform.getPlatformName();
        String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
        String port = extractPort(platform.getPlatformUrl());

        log.info("正在为平台 {} 配置ZSClient: {}:{}", platformName, processedUrl, port);

        try {
            // 配置ZSClient
            ZSClient.configure(new ZSConfig.Builder()
                    .setHostname(processedUrl)
                    .setPort(Convert.toInt(port))
                    .setContextPath("zstack")
                    .build());

            // 更新配置缓存
            platformConfigs.put(platformId, new PlatformConfig(platform.getPlatformUrl()));

            log.debug("平台 {} ZSClient配置完成", platformName);

        } catch (Exception e) {
            log.error("平台 {} ZSClient配置失败: {}", platformName, e.getMessage(), e);
            throw new RuntimeException("ZSClient配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 移除URL中的协议和端口部分
     *
     * @param url 原始URL
     * @return 处理后的主机名
     */
    private static String removeProtocolAndPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }

        url = url.trim();

        // 移除协议部分
        if (url.startsWith("http://")) {
            url = url.substring(7);
        } else if (url.startsWith("https://")) {
            url = url.substring(8);
        }

        // 移除端口部分
        int colonIndex = url.indexOf(':');
        if (colonIndex > 0) {
            url = url.substring(0, colonIndex);
        }

        // 移除路径部分
        int slashIndex = url.indexOf('/');
        if (slashIndex > 0) {
            url = url.substring(0, slashIndex);
        }

        return url;
    }

    /**
     * 提取URL中的端口号
     *
     * @param url 原始URL
     * @return 端口号字符串
     */
    private static String extractPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "8080"; // 默认端口
        }

        url = url.trim();

        // 移除协议部分
        if (url.startsWith("http://")) {
            url = url.substring(7);
        } else if (url.startsWith("https://")) {
            url = url.substring(8);
        }

        // 提取端口部分
        int colonIndex = url.indexOf(':');
        if (colonIndex > 0) {
            int slashIndex = url.indexOf('/', colonIndex);
            if (slashIndex > 0) {
                return url.substring(colonIndex + 1, slashIndex);
            } else {
                return url.substring(colonIndex + 1);
            }
        }

        return "8080"; // 默认端口
    }

    /**
     * 获取多平台路由统计信息
     *
     * @return 统计信息字符串
     */
    public static String getRouterStats() {
        return String.format("多平台路由统计 - 管理平台数: %d, 配置切换次数: %d, 当前活跃平台: %s",
                platformConfigs.size(), configSwitchCount, currentActivePlatformId);
    }

    /**
     * 清理指定平台的配置缓存
     *
     * @param platform 要清理的平台
     */
    public static void clearPlatformConfig(Platform platform) {
        if (platform != null && platform.getPlatformId() != null) {
            String platformId = platform.getPlatformId().toString();
            platformConfigs.remove(platformId);
            platformLocks.remove(platformId);

            if (platformId.equals(currentActivePlatformId)) {
                currentActivePlatformId = null;
            }

            log.info("已清理平台 {} 的配置缓存", platform.getPlatformName());
        }
    }

    /**
     * 清理所有平台配置缓存
     */
    public static void clearAllPlatformConfigs() {
        platformConfigs.clear();
        platformLocks.clear();
        currentActivePlatformId = null;
        configSwitchCount = 0;
        log.info("已清理所有平台配置缓存");
    }

    /**
     * 平台配置缓存类
     */
    private static class PlatformConfig {
        private final String url;
        private final long createTime;

        public PlatformConfig(String url) {
            this.url = url;
            this.createTime = System.currentTimeMillis();
        }

        public String getUrl() {
            return url;
        }

        public long getCreateTime() {
            return createTime;
        }
    }
}
