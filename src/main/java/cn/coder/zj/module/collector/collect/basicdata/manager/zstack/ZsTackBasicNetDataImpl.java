package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_NET;

@Slf4j
public class ZsTackBasicNetDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());

        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformInfo : platformList) {
            Platform platform = (Platform) platformInfo;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                try {
                    // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        //l2网卡
                        List<NetWorkL2Data> listL2 = collectDataL2(platform);
                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(listL2)
                                .metricsName(BASIC_NET.code())
                                .metricsType(MetricsType.BASIC_NET_L2.code())
                                .build()));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());

                        //l3网卡
                        List<NetWorkL3Data> listL3 = collectDataL3(platform, listL2);
                        ChannelHandlerContext ctx = CacheService.getCtx("ctx");
                        ClusterMsg.Message.Builder messageL3 = ClusterMsg.Message.newBuilder().setType(ClusterMsg.MessageType.BASIC)
                                .setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(listL3)
                                        .metricsName(BASIC_NET.code())
                                        .metricsType(MetricsType.BASIC_NET_L3.code())
                                        .build()));
                        messageL3.setTime(System.currentTimeMillis());
                        ctx.writeAndFlush(messageL3);

                        return listL2;
                    });
                } catch (Exception e) {
                    log.error("平台 {} 网络数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                }
            });
            String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
            log.info("collect basic data end, cost {} seconds", endTimeFormatted);
        }


    }

    private List<NetWorkL2Data> collectDataL2(Platform platform) {
        List<NetWorkL2Data> dataList = new ArrayList<>();
        String token = platform.getZsTackPlatform().getToken();
        QueryL2NetworkAction action = new QueryL2NetworkAction();
        if (platform.getAkType()==0){
            action.sessionId = token;
        }else {
            action.accessKeyId = platform.getUsername();
            action.accessKeySecret = platform.getPassword();
        }

        QueryL2NetworkAction.Result res = action.call();
        List inventories = res.value.getInventories();
        for (Object inventory : inventories) {
            if (inventory instanceof L2VlanNetworkInventory) {
                NetWorkL2Data netWorkInfo = new NetWorkL2Data();
                netWorkInfo.setName(((L2VlanNetworkInventory) inventory).name);
                netWorkInfo.setUuid(((L2VlanNetworkInventory) inventory).uuid);
                netWorkInfo.setPhysicalInterface(((L2VlanNetworkInventory) inventory).physicalInterface);
                netWorkInfo.setType(((L2VlanNetworkInventory) inventory).type);
                netWorkInfo.setVlan(String.valueOf(((L2VlanNetworkInventory) inventory).vlan));
                netWorkInfo.setVirtualNetworkId(((L2VlanNetworkInventory) inventory).virtualNetworkId);
                netWorkInfo.setPlatformId(platform.getPlatformId());
                netWorkInfo.setPlatformName(platform.getPlatformName());
                netWorkInfo.setRegionId(platform.getRegionId());
                netWorkInfo.setTypeName("zstack");
                netWorkInfo.setCreateTime(((L2VlanNetworkInventory) inventory).createDate);
                dataList.add(netWorkInfo);
            }
        }
        return dataList;
    }


    private List<NetWorkL3Data> collectDataL3(Platform platform, List<NetWorkL2Data> listL2) {
        List<NetWorkL3Data> dataList = new ArrayList<>();
            String token = platform.getZsTackPlatform().getToken();
            QueryL3NetworkAction l3action = new QueryL3NetworkAction();
            l3action.sessionId = token;
            if (platform.getAkType()==0){
                l3action.sessionId = token;
            }else{
                l3action.accessKeyId = platform.getUsername();
                l3action.accessKeySecret = platform.getPassword();
            }
            QueryL3NetworkAction.Result l3res = l3action.call();
            List<L3NetworkInventory> l3Inventories = l3res.value.inventories;
            for (L3NetworkInventory l3Inventory : l3Inventories) {
                NetWorkL3Data netWorkL3DTO = new NetWorkL3Data();
                netWorkL3DTO.setUuid(l3Inventory.getUuid());
                netWorkL3DTO.setL2NetworkUuid(l3Inventory.getL2NetworkUuid());
                netWorkL3DTO.setName(l3Inventory.getName());
                String l2NetworkName = "";
                // 二级网络名称
                Optional<NetWorkL2Data> matchingData = listL2.stream()
                        .filter(v -> v.getUuid().equals(l3Inventory.getL2NetworkUuid()))
                        .findFirst();
                if (matchingData.isPresent()) {
                    l2NetworkName = StringUtil.toString(matchingData.get().getName());
                }
                netWorkL3DTO.setL2NetworkName(l2NetworkName);
                String dns = "";
                List<String> jsonArrayDns = l3Inventory.getDns();
                if (jsonArrayDns != null && !jsonArrayDns.isEmpty()) {
                    dns = String.join(",", jsonArrayDns);
                }
                netWorkL3DTO.setDns(dns);
                netWorkL3DTO.setType(l3Inventory.getType());
                List<NetworkServiceL3NetworkRefInventory> services = l3Inventory.getNetworkServices();
                String networkServices = "";
                if (services.size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    for (int j = 0; j < services.size(); j++) {
                        NetworkServiceL3NetworkRefInventory service = services.get(j);
                        String networkServiceType = service.getNetworkServiceType();
                        if (j > 0) {
                            sb.append(",");
                        }
                        sb.append(networkServiceType);
                    }
                    networkServices = sb.toString();
                }
                netWorkL3DTO.setNetworkServices(networkServices);
                // ip详情
                List<IpRangeInventory> ipRanges = l3Inventory.getIpRanges();
                if (ipRanges.size() > 0) {
                    for (IpRangeInventory ipRange : ipRanges) {
                        netWorkL3DTO.setStartIp(ipRange.getStartIp());
                        netWorkL3DTO.setEndIp(ipRange.getEndIp());
                        netWorkL3DTO.setNetmask(ipRange.getNetmask());
                        netWorkL3DTO.setNetworkCidr(ipRange.getNetworkCidr());
                        netWorkL3DTO.setGateway(ipRange.getGateway());
                        netWorkL3DTO.setNetworkSegment(ipRange.getName());
                    }
                }
                List<L3NetworkHostRouteInventory> hostRoute = l3Inventory.getHostRoute();
                if (hostRoute.size() > 0) {
                    for (L3NetworkHostRouteInventory l3NetworkHostRouteInventory : hostRoute) {
                        netWorkL3DTO.setNextHopIp(l3NetworkHostRouteInventory.getNexthop());
                    }
                }
                netWorkL3DTO.setPlatformId(platform.getPlatformId());
                netWorkL3DTO.setPlatformName(platform.getPlatformName());
                netWorkL3DTO.setCreateTime(DateUtil.parse(Convert.toStr(l3Inventory.getCreateDate())));
                netWorkL3DTO.setTypeName("zstack");
                dataList.add(netWorkL3DTO);
            }


        return dataList;
    }


    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_NET.code();
    }
}
