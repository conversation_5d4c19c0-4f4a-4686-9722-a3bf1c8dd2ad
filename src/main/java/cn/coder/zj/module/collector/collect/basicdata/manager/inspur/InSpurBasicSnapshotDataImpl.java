package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.coder.zj.module.collector.util.ApiUtil.getInSpurJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_SNAPSHOT;

@Slf4j
public class InSpurBasicSnapshotDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeSnapshotData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_SNAPSHOT.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云快照采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeSnapshotData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + InSpurApiConstant.GET_CLOUD_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",platform.getInSpurPlatform().getToken()
        );
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VolumeSnapshotData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<VolumeSnapshotData> vmData = collectSnapshotData(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("浪潮云快照数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<VolumeSnapshotData> collectSnapshotData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        List<VolumeSnapshotData> list = new ArrayList<>();

        // 基础信息获取
        JsonObject cloud = jsonElement.getAsJsonObject();
        String cloudUri = getStringFromJson(cloud, "id");
        String cloudUuid = getStringFromJson(cloud, "uuid");
        String cloudName = getStringFromJson(cloud, "name");

        // 获取虚拟机下所有快照
        JsonArray jsonArray = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_CLOUD_LIST +"/"+ cloudUri + "/snapshots", null, headers);
        if (jsonArray == null || jsonArray.size() == 0) {
            return list;
        }

        for (JsonElement rootSnapshot : jsonArray) {
            JsonObject snapshot = rootSnapshot.getAsJsonObject();
            List<JsonObject> snapshots = new ArrayList<>();
            findSnapshots(snapshot, snapshots);

            if (snapshots.isEmpty()) {
                continue;
            }

            for (JsonObject jsonObject : snapshots) {
                String id = getStringFromJson(jsonObject, "id");
                JsonObject info = FsApiCacheService.getJsonObject(
                        platformUrl + InSpurApiConstant.GET_CLOUD_LIST + "/snapshots/" + id,
                        null,
                        headers
                );

                if (info == null) {
                    continue;
                }

                // 创建快照DTO并设置基本属性
                VolumeSnapshotData volumeSnapshotDTO = new VolumeSnapshotData();

                // 设置基本信息
                String createTimeStr = getStringFromJson(info, "createTime");
                Date createTime = convertStringToDate(createTimeStr);

                volumeSnapshotDTO.setName(getStringFromJson(info, "name"));
                volumeSnapshotDTO.setDescription(getStringFromJson(info, "description"));
                volumeSnapshotDTO.setCreateTime(createTime);
                volumeSnapshotDTO.setHostUuid(cloudUri);
                volumeSnapshotDTO.setHostName(cloudName);
                volumeSnapshotDTO.setType("主机快照");
                volumeSnapshotDTO.setLatest("true");
                volumeSnapshotDTO.setPlatformName(platform.getPlatformName());
                volumeSnapshotDTO.setPlatformId(platform.getPlatformId());
                volumeSnapshotDTO.setTypeName("inspur");
                volumeSnapshotDTO.setStatus("Enabled");
                volumeSnapshotDTO.setVCreateDate(createTime);
                volumeSnapshotDTO.setVUpdateDate(new Date());
                volumeSnapshotDTO.setDeleted(0);
                volumeSnapshotDTO.setSize(getLongFromJsonDouble(info, "sizeInByte"));
                volumeSnapshotDTO.setIsMemory(getBooleanFromJson(info, "memory"));
                volumeSnapshotDTO.setUuid(id);

                // 设置存储相关信息
                if (info.has("disks") && !info.get("disks").isJsonNull()) {
                    JsonArray diskArray = info.getAsJsonArray("disks");
                    if (diskArray != null && diskArray.size() > 0) {
                        JsonObject diskObj = diskArray.get(0).getAsJsonObject();
                        if (diskObj.has("volume") && !diskObj.get("volume").isJsonNull()) {
                            JsonObject volumeObj = diskObj.getAsJsonObject("volume");
                            volumeSnapshotDTO.setPrimaryStorageUuid(getStringFromJson(volumeObj, "dataStoreId"));
                            volumeSnapshotDTO.setPrimaryStorageName(getStringFromJson(volumeObj, "dataStoreName"));
                            volumeSnapshotDTO.setInstallPath(getStringFromJson(volumeObj, "fileName"));
                            volumeSnapshotDTO.setFormat(getStringFromJson(volumeObj, "format").toLowerCase());
                        }
                    }
                }

                list.add(volumeSnapshotDTO);
            }
        }

        return list;
    }

    public static Date convertStringToDate(String dateString) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateString);
        } catch (ParseException e) {
            return new Date();
        }
    }

    private void findSnapshots(JsonObject node, List<JsonObject> snapshots) {
        if (node.has("children") && node.get("children").isJsonArray()) {
            JsonArray children = node.getAsJsonArray("children");
            for (JsonElement child : children) {
                if (child.isJsonObject()) {
                    JsonObject childObj = child.getAsJsonObject();
                    // 只有当id属性存在且不为空时，才将其添加到快照列表
                    if (childObj.has("id") && !childObj.get("id").isJsonNull() &&
                            !childObj.get("id").getAsString().isEmpty()) {
                        snapshots.add(childObj);
                    }
                    findSnapshots(childObj, snapshots);
                }
            }
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_SNAPSHOT.code();
    }
}
