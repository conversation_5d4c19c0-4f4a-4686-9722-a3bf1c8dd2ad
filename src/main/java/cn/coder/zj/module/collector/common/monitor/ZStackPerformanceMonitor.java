package cn.coder.zj.module.collector.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * <p>
 * ZStack性能监控组件
 * <p>
 * 用于收集和统计ZStack数据收集系统的性能指标，包括锁等待时间、配置切换频率、并发度使用情况等
 * <p>
 * 核心功能：
 * 1. 锁等待时间统计
 * 2. 配置切换频率监控
 * 3. 并发度使用情况统计
 * 4. 性能报告生成
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ZStackPerformanceMonitor {

    // 锁等待时间统计
    private final LongAdder totalLockWaitTime = new LongAdder();
    private final AtomicLong lockWaitCount = new AtomicLong(0);
    private final AtomicLong maxLockWaitTime = new AtomicLong(0);

    // 配置切换统计
    private final AtomicLong configSwitchCount = new AtomicLong(0);
    private final AtomicLong lastConfigSwitchTime = new AtomicLong(0);

    // 并发度使用统计 - 按平台ID统计
    private final ConcurrentHashMap<String, AtomicLong> platformConcurrencyUsage = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> platformMaxConcurrency = new ConcurrentHashMap<>();

    // 操作执行时间统计
    private final LongAdder totalExecutionTime = new LongAdder();
    private final AtomicLong executionCount = new AtomicLong(0);
    private final AtomicLong maxExecutionTime = new AtomicLong(0);

    // 错误统计
    private final AtomicLong lockTimeoutCount = new AtomicLong(0);
    private final AtomicLong operationFailureCount = new AtomicLong(0);

    // 监控开始时间
    private final long monitorStartTime = System.currentTimeMillis();

    /**
     * 记录锁等待时间
     *
     * @param platformName 平台名称
     * @param waitTimeMs   等待时间（毫秒）
     */
    public void recordLockWaitTime(String platformName, long waitTimeMs) {
        if (waitTimeMs < 0) {
            log.warn("无效的锁等待时间: {} ms, 平台: {}", waitTimeMs, platformName);
            return;
        }

        totalLockWaitTime.add(waitTimeMs);
        lockWaitCount.incrementAndGet();

        // 更新最大等待时间
        long currentMax = maxLockWaitTime.get();
        while (waitTimeMs > currentMax && !maxLockWaitTime.compareAndSet(currentMax, waitTimeMs)) {
            currentMax = maxLockWaitTime.get();
        }

        // 记录超长等待时间
        if (waitTimeMs > 5000) { // 超过5秒
            log.warn("平台 {} 锁等待时间过长: {} ms", platformName, waitTimeMs);
        }
    }

    /**
     * 记录配置切换事件
     *
     * @param platformName 平台名称
     */
    public void recordConfigSwitchCount(String platformName) {
        configSwitchCount.incrementAndGet();
        lastConfigSwitchTime.set(System.currentTimeMillis());
        log.debug("平台 {} 配置切换，总切换次数: {}", platformName, configSwitchCount.get());
    }

    /**
     * 记录并发度使用情况
     *
     * @param platformId        平台ID
     * @param currentConcurrency 当前并发数
     */
    public void recordConcurrencyUsage(String platformId, int currentConcurrency) {
        if (currentConcurrency < 0) {
            log.warn("无效的并发度: {}, 平台ID: {}", currentConcurrency, platformId);
            return;
        }

        // 更新当前并发度
        platformConcurrencyUsage.computeIfAbsent(platformId, k -> new AtomicLong(0))
                .set(currentConcurrency);

        // 更新最大并发度
        AtomicLong maxConcurrency = platformMaxConcurrency.computeIfAbsent(platformId, k -> new AtomicLong(0));
        long currentMax = maxConcurrency.get();
        while (currentConcurrency > currentMax && !maxConcurrency.compareAndSet(currentMax, currentConcurrency)) {
            currentMax = maxConcurrency.get();
        }
    }

    /**
     * 记录操作执行时间（参考RealtimePerfMonitor模式）
     *
     * @param platformName  平台名称
     * @param executionTime 执行时间（毫秒）
     */
    public void recordExecutionTime(String platformName, long executionTime) {
        if (executionTime < 0) {
            log.warn("无效的执行时间: {} ms, 平台: {}", executionTime, platformName);
            return;
        }

        totalExecutionTime.add(executionTime);
        executionCount.incrementAndGet();

        // 更新最大执行时间
        long currentMax = maxExecutionTime.get();
        while (executionTime > currentMax && !maxExecutionTime.compareAndSet(currentMax, executionTime)) {
            currentMax = maxExecutionTime.get();
        }

        // 记录执行时间超过1秒的操作（参考RealtimePerfMonitor模式）
        if (executionTime > 1000) {
            log.info("平台 {} 操作执行耗时: {} ms", platformName, executionTime);
        }
    }

    /**
     * 记录锁超时事件
     *
     * @param platformName 平台名称
     */
    public void recordLockTimeout(String platformName) {
        lockTimeoutCount.incrementAndGet();
        log.error("平台 {} 发生锁超时", platformName);
    }

    /**
     * 记录操作失败事件
     *
     * @param platformName 平台名称
     * @param errorMessage 错误信息
     */
    public void recordOperationFailure(String platformName, String errorMessage) {
        operationFailureCount.incrementAndGet();
        log.error("平台 {} 操作失败: {}", platformName, errorMessage);
    }

    /**
     * 获取性能统计报告
     *
     * @return 格式化的性能统计信息
     */
    public String getPerformanceStats() {
        long currentTime = System.currentTimeMillis();
        long monitorDuration = currentTime - monitorStartTime;
        double monitorDurationMinutes = monitorDuration / 60000.0;

        StringBuilder stats = new StringBuilder();
        stats.append("=== ZStack性能监控报告 ===\n");
        stats.append(String.format("监控时长: %.2f 分钟\n", monitorDurationMinutes));
        stats.append("\n");

        // 锁等待统计
        long waitCount = lockWaitCount.get();
        if (waitCount > 0) {
            double avgWaitTime = totalLockWaitTime.doubleValue() / waitCount;
            stats.append("锁等待统计:\n");
            stats.append(String.format("  总等待次数: %d\n", waitCount));
            stats.append(String.format("  平均等待时间: %.2f ms\n", avgWaitTime));
            stats.append(String.format("  最大等待时间: %d ms\n", maxLockWaitTime.get()));
        } else {
            stats.append("锁等待统计: 无等待记录\n");
        }
        stats.append("\n");

        // 配置切换统计
        stats.append("配置切换统计:\n");
        stats.append(String.format("  总切换次数: %d\n", configSwitchCount.get()));
        if (lastConfigSwitchTime.get() > 0) {
            long timeSinceLastSwitch = currentTime - lastConfigSwitchTime.get();
            stats.append(String.format("  距离上次切换: %d ms\n", timeSinceLastSwitch));
        }
        stats.append("\n");

        // 并发度统计
        stats.append("并发度统计:\n");
        if (platformConcurrencyUsage.isEmpty()) {
            stats.append("  无并发度记录\n");
        } else {
            platformConcurrencyUsage.forEach((platformId, currentConcurrency) -> {
                AtomicLong maxConcurrency = platformMaxConcurrency.get(platformId);
                stats.append(String.format("  平台 %s: 当前=%d, 最大=%d\n",
                        platformId, currentConcurrency.get(),
                        maxConcurrency != null ? maxConcurrency.get() : 0));
            });
        }
        stats.append("\n");

        // 执行时间统计
        long execCount = executionCount.get();
        if (execCount > 0) {
            double avgExecutionTime = totalExecutionTime.doubleValue() / execCount;
            stats.append("执行时间统计:\n");
            stats.append(String.format("  总执行次数: %d\n", execCount));
            stats.append(String.format("  平均执行时间: %.2f ms\n", avgExecutionTime));
            stats.append(String.format("  最大执行时间: %d ms\n", maxExecutionTime.get()));
        } else {
            stats.append("执行时间统计: 无执行记录\n");
        }
        stats.append("\n");

        // 错误统计
        stats.append("错误统计:\n");
        stats.append(String.format("  锁超时次数: %d\n", lockTimeoutCount.get()));
        stats.append(String.format("  操作失败次数: %d\n", operationFailureCount.get()));

        return stats.toString();
    }

    /**
     * 重置所有统计数据
     */
    public void resetStats() {
        totalLockWaitTime.reset();
        lockWaitCount.set(0);
        maxLockWaitTime.set(0);
        configSwitchCount.set(0);
        lastConfigSwitchTime.set(0);
        platformConcurrencyUsage.clear();
        platformMaxConcurrency.clear();
        totalExecutionTime.reset();
        executionCount.set(0);
        maxExecutionTime.set(0);
        lockTimeoutCount.set(0);
        operationFailureCount.set(0);
        log.info("ZStack性能监控统计数据已重置");
    }

    /**
     * 获取简化的性能摘要
     *
     * @return 简化的性能摘要信息
     */
    public String getPerformanceSummary() {
        long waitCount = lockWaitCount.get();
        long execCount = executionCount.get();
        double avgWaitTime = waitCount > 0 ? totalLockWaitTime.doubleValue() / waitCount : 0;
        double avgExecTime = execCount > 0 ? totalExecutionTime.doubleValue() / execCount : 0;

        return String.format("ZStack性能摘要 - 锁等待: %d次/%.1fms平均, 执行: %d次/%.1fms平均, 配置切换: %d次, 错误: %d次",
                waitCount, avgWaitTime, execCount, avgExecTime, configSwitchCount.get(),
                lockTimeoutCount.get() + operationFailureCount.get());
    }
}
