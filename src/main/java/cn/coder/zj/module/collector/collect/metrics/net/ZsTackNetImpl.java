package cn.coder.zj.module.collector.collect.metrics.net;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_IN_TASK;
import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_OUT_TASK;
import static cn.coder.zj.module.collector.enums.NetType.PROTOCOL_ZS_TACK_NET;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackNetImpl extends AbstractMetrics {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                try {
                    Platform platform = (Platform) o;
                    // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        List<MetricData> metricDataList = new ArrayList<>();
                        String token = platform.getZsTackPlatform().getToken();
                        if (token == null && platform.getAkType() == 0) {
                            log.error("平台 {} token为空", platform.getPlatformName());
                            return null;
                        }

                        Long platformId = platform.getPlatformId();
                        // 获取所有VM的UUID并组合成标签
                        List<MonitorInfo> vmUuids = platform.getZsTackPlatform().getVmUuids();
                        List<MonitorInfo> hostUuids = platform.getZsTackPlatform().getHostUuids();
                        if (vmUuids == null || vmUuids.isEmpty()) {
                            log.error("平台 {} VM UUID为空", platform.getPlatformName());
                            return null;
                        }

                        networkOutTask(vmUuids, hostUuids, token, platform, metricDataList);
                        networkInTask(vmUuids, hostUuids, token, platform, metricDataList);

                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.NET_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect collectData data end, cost {} seconds 平台 {}", endTimeFormatted ,platform.getPlatformName());
                        return metricDataList;
                    });
                } catch (Exception e) {
                    log.error("平台 {} 网络指标收集失败: {}", ((Platform) o).getPlatformName(), e.getMessage(), e);
                }
            });
        }
    }

    private List<MetricData> networkInTask(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, String token, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, token, "ZStack/VM", "VMUuid=", platform, NETWORK_IN_TASK.code(), metricDataList);
        getMetricHostData(hostUuids, token, "ZStack/Host", "HostUuid=", platform, NETWORK_IN_TASK.code(), metricDataList);
        return metricDataList;
    }

    private List<MetricData> networkOutTask(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, String token, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, token, "ZStack/VM", "VMUuid=", platform, NETWORK_OUT_TASK.code(), metricDataList);
        getMetricHostData(hostUuids, token, "ZStack/Host", "HostUuid=", platform, NETWORK_OUT_TASK.code(), metricDataList);
        return metricDataList;
    }

    private List<MetricData> getMetricVmData(List<MonitorInfo> vmUuids, String token, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList) {
        return getMetricData(vmUuids, token, namespace, labelPrefix, platform, code, metricDataList,"vm");
    }

    private List<MetricData> getMetricHostData(List<MonitorInfo> hostUuids, String token, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList) {
        return getMetricData(hostUuids, token, namespace, labelPrefix, platform, code, metricDataList,"host");
    }

    private List<MetricData> getMetricData(List<MonitorInfo> uuids, String token, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList,String type) {
        for (MonitorInfo monitorInfo : uuids) {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = code;
            action.labels = asList(labelPrefix.concat(monitorInfo.getUuid()));
            if (platform.getAkType() == 0) {
                action.sessionId = token;
            } else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }
            action.period = 20;
            action.startTime = System.currentTimeMillis() / 1000 - 60;  // 60秒前
            action.endTime = System.currentTimeMillis() / 1000;         // 当前时间

            MetricData metricData = new MetricData();
            metricData.setResourceId(monitorInfo.getUuid());
            metricData.setResourceName(monitorInfo.getName());
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setType(type);
            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            GetMetricDataAction.Result result = action.call();
            if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                timestamps.add(dataObj.get("time").getAsLong());
                values.add(dataObj.get("value").getAsDouble());
                metricData.setMetricName(code);
                metricData.setTimestamps(timestamps);
                metricData.setValues(values);
                metricDataList.add(metricData);
            }

        }
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_ZS_TACK_NET.code();
    }
}
