package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.common.config.ZStackConcurrencyProperties;
import cn.coder.zj.module.collector.common.monitor.ZStackPerformanceMonitor;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.zstack.sdk.ZSClient;
import org.zstack.sdk.ZSConfig;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Supplier;

/**
 * {{RIPER-5:
 * Action: "Added"
 * Task_ID: "243631cd-49bf-444a-8cee-fca8f0fea542"
 * Timestamp: "2025-08-01T10:53:38+08:00"
 * Authoring_Role: "LD"
 * Principle_Applied: "SOLID-S (单一职责原则) - 专门负责优化的多平台配置路由和并发控制"
 * Quality_Check: "分层锁机制，读写锁+信号量，支持锁升级降级，无死锁设计，完整性能监控"
 * }}
 * <p>
 * ZStack多平台路由器优化版
 * <p>
 * 解决ZStack SDK全局配置冲突问题，支持多平台高并发访问
 * <p>
 * 核心优化：
 * 1. 分层锁机制：配置读写锁 + 平台信号量
 * 2. 锁升级/降级：读锁用于操作，写锁用于配置
 * 3. 并发控制：同一平台允许有限并发操作
 * 4. 性能监控：集成完整的性能指标收集
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ZStackMultiPlatformRouterOptimized {

    // 配置读写锁 - 分离配置操作和数据操作
    private static final ReentrantReadWriteLock configLock = new ReentrantReadWriteLock();
    
    // 平台信号量 - 控制每个平台的并发度
    private static final ConcurrentHashMap<String, Semaphore> platformSemaphores = new ConcurrentHashMap<>();
    
    // 平台配置缓存（复用原有逻辑）
    private static final ConcurrentHashMap<String, PlatformConfig> platformConfigs = new ConcurrentHashMap<>();
    
    // 当前活跃平台ID
    private static volatile String currentActivePlatformId = null;
    
    // 配置切换统计
    private static volatile long configSwitchCount = 0;

    @Autowired
    private ZStackConcurrencyProperties concurrencyProperties;
    
    @Autowired
    private ZStackPerformanceMonitor performanceMonitor;

    /**
     * 优化的平台操作执行方法
     * <p>
     * 核心优化：使用分层锁机制，支持同一平台的并发操作
     *
     * @param platform  平台信息
     * @param operation 要执行的操作
     * @param <T>       返回值类型
     * @return 操作结果
     * @throws RuntimeException 当平台参数无效或操作执行失败时
     */
    public <T> T executeWithPlatformOptimized(Platform platform, Supplier<T> operation) {
        if (platform == null) {
            throw new IllegalArgumentException("平台参数不能为空");
        }

        if (platform.getPlatformId() == null) {
            throw new IllegalArgumentException("平台ID不能为空");
        }

        String platformId = platform.getPlatformId().toString();
        String platformName = platform.getPlatformName();
        
        long startTime = System.currentTimeMillis();
        long lockWaitStart = startTime;

        try {
            // 1. 获取读锁进行操作
            configLock.readLock().lock();
            long lockWaitTime = System.currentTimeMillis() - lockWaitStart;
            performanceMonitor.recordLockWaitTime(platformName, lockWaitTime);
            
            try {
                // 2. 检查是否需要配置切换
                if (needReconfigure(platform)) {
                    // 需要配置切换时，升级为写锁
                    configLock.readLock().unlock();
                    
                    long writeLockWaitStart = System.currentTimeMillis();
                    boolean writeLockAcquired = false;
                    
                    try {
                        // 尝试获取写锁，支持超时
                        writeLockAcquired = configLock.writeLock().tryLock(
                            concurrencyProperties.getLockTimeoutMs(), TimeUnit.MILLISECONDS);
                        
                        if (!writeLockAcquired) {
                            performanceMonitor.recordLockTimeout(platformName);
                            throw new RuntimeException("获取配置写锁超时: " + platformName);
                        }
                        
                        long writeLockWaitTime = System.currentTimeMillis() - writeLockWaitStart;
                        performanceMonitor.recordLockWaitTime(platformName, writeLockWaitTime);
                        
                        // 双重检查，避免重复配置
                        if (needReconfigure(platform)) {
                            configureZSClient(platform);
                            currentActivePlatformId = platformId;
                            configSwitchCount++;
                            performanceMonitor.recordConfigSwitchCount(platformName);
                            log.debug("已为平台 {} 切换ZSClient配置 (总切换次数: {})", platformName, configSwitchCount);
                        }
                        
                        // 降级为读锁
                        configLock.readLock().lock();
                        
                    } finally {
                        if (writeLockAcquired) {
                            configLock.writeLock().unlock();
                        }
                    }
                }

                // 3. 获取平台信号量，控制并发度
                Semaphore platformSemaphore = platformSemaphores.computeIfAbsent(platformId, 
                    k -> new Semaphore(concurrencyProperties.getPlatformConcurrency()));
                
                long semaphoreWaitStart = System.currentTimeMillis();
                boolean semaphoreAcquired = false;
                
                try {
                    // 尝试获取信号量许可
                    semaphoreAcquired = platformSemaphore.tryAcquire(
                        concurrencyProperties.getLockTimeoutMs(), TimeUnit.MILLISECONDS);
                    
                    if (!semaphoreAcquired) {
                        performanceMonitor.recordLockTimeout(platformName);
                        throw new RuntimeException("获取平台并发许可超时: " + platformName);
                    }
                    
                    long semaphoreWaitTime = System.currentTimeMillis() - semaphoreWaitStart;
                    performanceMonitor.recordLockWaitTime(platformName, semaphoreWaitTime);
                    
                    // 记录并发度使用情况
                    int currentConcurrency = concurrencyProperties.getPlatformConcurrency() - platformSemaphore.availablePermits();
                    performanceMonitor.recordConcurrencyUsage(platformId, currentConcurrency);
                    
                    log.debug("平台 {} 开始执行操作，当前并发度: {}/{}", 
                        platformName, currentConcurrency, concurrencyProperties.getPlatformConcurrency());

                    // 4. 执行实际操作
                    long operationStartTime = System.currentTimeMillis();
                    T result = operation.get();
                    long operationDuration = System.currentTimeMillis() - operationStartTime;
                    
                    performanceMonitor.recordExecutionTime(platformName, operationDuration);
                    log.debug("平台 {} 操作完成，耗时: {}ms", platformName, operationDuration);
                    
                    return result;
                    
                } finally {
                    if (semaphoreAcquired) {
                        platformSemaphore.release();
                        
                        // 更新并发度统计
                        int updatedConcurrency = concurrencyProperties.getPlatformConcurrency() - platformSemaphore.availablePermits();
                        performanceMonitor.recordConcurrencyUsage(platformId, updatedConcurrency);
                    }
                }
                
            } finally {
                configLock.readLock().unlock();
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            performanceMonitor.recordOperationFailure(platformName, "线程中断: " + e.getMessage());
            throw new RuntimeException("操作被中断: " + platformName, e);
        } catch (Exception e) {
            performanceMonitor.recordOperationFailure(platformName, e.getMessage());
            log.error("平台 {} 操作执行失败: {}", platformName, e.getMessage(), e);
            throw new RuntimeException("ZStack多平台操作执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否需要重新配置ZSClient（复用原有逻辑）
     */
    private boolean needReconfigure(Platform platform) {
        String platformId = platform.getPlatformId().toString();

        // 如果当前活跃平台不是目标平台，需要重新配置
        if (!platformId.equals(currentActivePlatformId)) {
            return true;
        }

        // 检查配置是否已缓存且有效
        PlatformConfig cachedConfig = platformConfigs.get(platformId);
        if (cachedConfig == null) {
            return true;
        }

        // 检查配置是否变更（URL或端口变化）
        String currentUrl = platform.getPlatformUrl();
        return !cachedConfig.getUrl().equals(currentUrl);
    }

    /**
     * 配置ZSClient为指定平台（复用原有逻辑）
     */
    private void configureZSClient(Platform platform) {
        String platformId = platform.getPlatformId().toString();
        String platformName = platform.getPlatformName();
        String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
        String port = extractPort(platform.getPlatformUrl());

        log.info("正在为平台 {} 配置ZSClient: {}:{}", platformName, processedUrl, port);

        try {
            // 配置ZSClient
            ZSClient.configure(new ZSConfig.Builder()
                    .setHostname(processedUrl)
                    .setPort(Convert.toInt(port))
                    .setContextPath("zstack")
                    .build());

            // 更新配置缓存
            platformConfigs.put(platformId, new PlatformConfig(platform.getPlatformUrl()));

            log.debug("平台 {} ZSClient配置完成", platformName);

        } catch (Exception e) {
            log.error("平台 {} ZSClient配置失败: {}", platformName, e.getMessage(), e);
            throw new RuntimeException("ZSClient配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 移除URL中的协议和端口部分（复用原有逻辑）
     */
    private String removeProtocolAndPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            return url;
        }

        String result = url.trim();
        
        // 移除协议部分
        if (result.startsWith("http://")) {
            result = result.substring(7);
        } else if (result.startsWith("https://")) {
            result = result.substring(8);
        }
        
        // 移除端口部分
        int colonIndex = result.indexOf(':');
        if (colonIndex != -1) {
            result = result.substring(0, colonIndex);
        }
        
        return result;
    }

    /**
     * 提取URL中的端口部分（复用原有逻辑）
     */
    private String extractPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "8080"; // 默认端口
        }

        String result = url.trim();
        
        // 移除协议部分
        if (result.startsWith("http://")) {
            result = result.substring(7);
        } else if (result.startsWith("https://")) {
            result = result.substring(8);
        }
        
        // 提取端口部分
        int colonIndex = result.indexOf(':');
        if (colonIndex != -1 && colonIndex < result.length() - 1) {
            return result.substring(colonIndex + 1);
        }
        
        return "8080"; // 默认端口
    }

    /**
     * 获取优化路由统计信息
     */
    public String getOptimizedRouterStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== ZStack优化路由统计 ===\n");
        stats.append(String.format("管理平台数: %d\n", platformConfigs.size()));
        stats.append(String.format("配置切换次数: %d\n", configSwitchCount));
        stats.append(String.format("当前活跃平台: %s\n", currentActivePlatformId));
        stats.append("\n平台并发度配置:\n");
        
        platformSemaphores.forEach((platformId, semaphore) -> {
            int available = semaphore.availablePermits();
            int total = concurrencyProperties.getPlatformConcurrency();
            int inUse = total - available;
            stats.append(String.format("  平台 %s: %d/%d (使用中/总数)\n", platformId, inUse, total));
        });
        
        return stats.toString();
    }

    /**
     * 清理指定平台的配置缓存
     */
    public void clearPlatformConfig(Platform platform) {
        if (platform != null && platform.getPlatformId() != null) {
            String platformId = platform.getPlatformId().toString();
            platformConfigs.remove(platformId);
            platformSemaphores.remove(platformId);

            if (platformId.equals(currentActivePlatformId)) {
                currentActivePlatformId = null;
            }

            log.info("已清理平台 {} 的优化配置缓存", platform.getPlatformName());
        }
    }

    /**
     * 清理所有平台配置缓存
     */
    public void clearAllPlatformConfigs() {
        platformConfigs.clear();
        platformSemaphores.clear();
        currentActivePlatformId = null;
        configSwitchCount = 0;
        log.info("已清理所有优化平台配置缓存");
    }

    /**
     * 平台配置缓存类（复用原有逻辑）
     */
    private static class PlatformConfig {
        private final String url;
        private final long createTime;

        public PlatformConfig(String url) {
            this.url = url;
            this.createTime = System.currentTimeMillis();
        }

        public String getUrl() {
            return url;
        }

        public long getCreateTime() {
            return createTime;
        }
    }
}
