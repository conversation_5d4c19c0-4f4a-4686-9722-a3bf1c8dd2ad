package cn.coder.zj.module.collector.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * }}
 * <p>
 * ZStack并发控制配置类
 * <p>
 * 用于管理ZStack数据收集系统的并发控制参数，支持锁机制优化配置
 * <p>
 * 核心配置项：
 * 1. 平台并发度控制
 * 2. 优化机制开关
 * 3. 锁超时时间设置
 * 4. 性能监控开关
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "zstack.concurrency")
public class ZStackConcurrencyProperties {

    /**
     * 每个平台允许的并发操作数
     * <p>
     * 默认值：3
     * 建议范围：1-10，根据平台性能和网络状况调整
     */
    @Value("${zstack.concurrency.platform-concurrency:3}")
    private int platformConcurrency;

    /**
     * 是否启用锁机制优化
     * <p>
     * 默认值：false（保守策略，需要手动开启）
     * true：使用优化的分层锁机制（读写锁+信号量）
     * false：使用原有的排他锁机制
     */
    @Value("${zstack.concurrency.enable-optimization:false}")
    private boolean enableOptimization;

    /**
     * 锁获取超时时间（毫秒）
     * <p>
     * 默认值：30000ms（30秒）
     * 超过此时间未获取到锁将抛出超时异常
     */
    @Value("${zstack.concurrency.lock-timeout-ms:30000}")
    private long lockTimeoutMs;

    /**
     * 配置切换检查间隔（毫秒）
     * <p>
     * 默认值：5000ms（5秒）
     * 用于定期检查平台配置是否需要更新
     */
    @Value("${zstack.concurrency.config-check-interval-ms:5000}")
    private long configCheckIntervalMs;

    /**
     * 是否启用性能监控
     * <p>
     * 默认值：true
     * 启用后会收集锁等待时间、配置切换频率等性能指标
     */
    @Value("${zstack.concurrency.enable-performance-monitoring:true}")
    private boolean enablePerformanceMonitoring;

    /**
     * 性能统计报告间隔（毫秒）
     * <p>
     * 默认值：60000ms（1分钟）
     * 定期输出性能统计报告的间隔时间
     */
    @Value("${zstack.concurrency.performance-report-interval-ms:60000}")
    private long performanceReportIntervalMs;

    /**
     * 最大配置缓存数量
     * <p>
     * 默认值：100
     * 防止内存泄漏，限制平台配置缓存的最大数量
     */
    @Value("${zstack.concurrency.max-config-cache-size:100}")
    private int maxConfigCacheSize;

    /**
     * 配置缓存过期时间（毫秒）
     * <p>
     * 默认值：3600000ms（1小时）
     * 配置缓存的过期时间，过期后会重新加载
     */
    @Value("${zstack.concurrency.config-cache-expire-ms:3600000}")
    private long configCacheExpireMs;
}
