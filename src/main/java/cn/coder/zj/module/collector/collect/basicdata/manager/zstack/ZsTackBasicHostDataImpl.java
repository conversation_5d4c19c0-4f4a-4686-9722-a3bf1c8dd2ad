package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;


import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicReference;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_HOST;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackBasicHostDataImpl extends AbstractBasicData {


    protected long startTime;


    private static final BigDecimal ZERO = new BigDecimal(0);
    private static final String KVM = "KVM";
    private static final String HOST_UUID_PREFIX = "HostUuid=";

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);

        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }

            try {
                // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                    List<HostData> hostDataList = new ArrayList<>();

                    String token = platform.getZsTackPlatform().getToken();
                    if (token == null && platform.getAkType() == 0) {
                        log.error("平台 {} token为空", platform.getPlatformName());
                        return null;
                    }

                    // 查询区域列表
                    QueryZoneAction zoneAction = new QueryZoneAction();
                    if (platform.getAkType() == 0) {
                        zoneAction.sessionId = token;
                    } else {
                        zoneAction.accessKeyId = platform.getUsername();
                        zoneAction.accessKeySecret = platform.getPassword();
                    }
                    List<?> regionList = zoneAction.call().value.inventories;

                    // 查询主机列表
                    QueryHostAction hostAction = new QueryHostAction();
                    if (platform.getAkType() == 0) {
                        hostAction.sessionId = token;
                    } else {
                        hostAction.accessKeyId = platform.getUsername();
                        hostAction.accessKeySecret = platform.getPassword();
                    }
                    QueryHostAction.Result hostResult = hostAction.call();

                    if (hostResult.value != null) {
                        processHostList(hostResult.value.inventories, platform, token, regionList, hostDataList);
                    }

                    if (!CollUtil.isEmpty(hostDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(hostDataList)
                                .metricsName(BASIC_HOST.code())
                                .build();
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    }

                    return hostDataList;
                });
            } catch (Exception e) {
                log.error("平台 {} 主机数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
            }

        }
    }


    private void processHostList(List<?> hostList, Platform platform, String token, List<?> regionList, List<HostData> hostDataList) {
        for (Object host : hostList) {
            JsonObject jsonObject = GsonUtil.GSON.toJsonTree(host).getAsJsonObject();
            JsonElement hypervisorType = jsonObject.get("hypervisorType");
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            if (hypervisorType != null && !hypervisorType.isJsonNull() &&
                    KVM.equals(hypervisorType.getAsString()) && uuid != null) {
                processHostData(jsonObject, platform, token, regionList, hostDataList);
            }
        }
    }


    private void processHostData(JsonObject jsonObject, Platform platform, String token, List<?> regionList, List<HostData> hostDataS) {
        try {
            HostData hostData = new HostData();
            AtomicReference<String> clusterName = new AtomicReference<>();
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            String name = getStringFromJson(jsonObject, "name", "-");
            String state = getStringFromJson(jsonObject, "state", "-");
            String ip = getStringFromJson(jsonObject, "managementIp", "-");
            String status = getStringFromJson(jsonObject, "status", "-");
            String clusterUuid = getStringFromJson(jsonObject, "clusterUuid", "");
            String zoneUuid = getStringFromJson(jsonObject, "zoneUuid", "");
            hostData.setManager("zstack");
            hostData.setAvailableManager("-");
            // 设置区域名称
            for (Object item : regionList) {
                if (item instanceof ZoneInventory) {
                    ZoneInventory zone = (ZoneInventory) item;
                    if (zone.getUuid().equals(zoneUuid)) {
                        hostData.setAvailableManager(zone.getName());
                        break;
                    }
                }
            }
            // CPU相关数据
            Long totalCpuCapacity = getLongFromJson(jsonObject, "totalCpuCapacity");
            Long availableCpuCapacity = getLongFromJson(jsonObject, "availableCpuCapacity");
            Integer cpuSockets = getIntFromJson(jsonObject, "cpuSockets");
            String architecture = getStringFromJson(jsonObject, "architecture", "-");
            Integer cpuNum = getIntFromJson(jsonObject, "cpuNum");
            BigDecimal cpuCommitRate = new BigDecimal(totalCpuCapacity - availableCpuCapacity);
            // 内存相关数据
            Long totalMemoryCapacity = getLongFromJson(jsonObject, "totalMemoryCapacity");
            Long availableMemoryCapacity = getLongFromJson(jsonObject, "availableMemoryCapacity");
            BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity - availableMemoryCapacity);
            // 创建日期
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy, h:mm:ss a", Locale.US);
            Date createDate;
            try {
                createDate = sdf.parse("Aug 19, 2020, 5:25:01 PM");
            } catch (ParseException e) {
                createDate = new Date();
                log.error("解析日期失败", e);
            }
            // 获取集群名称
            QueryClusterAction clusters = new QueryClusterAction();
            clusters.conditions = asList("hypervisorType=KVM");
            if (platform.getAkType() == 0) {
                clusters.sessionId = token;
            } else {
                clusters.accessKeyId = platform.getUsername();
                clusters.accessKeySecret = platform.getPassword();
            }
            clusters.call().value.inventories.forEach(cluster -> {
                JsonObject clusterObj = GsonUtil.GSON.toJsonTree(cluster).getAsJsonObject();
                if (clusterUuid.equals(clusterObj.get("uuid").getAsString())) {
                    clusterName.set(clusterObj.get("name").getAsString());
                }
            });

            // 获取各种指标数据
            BigDecimal memory_used = getMetricData("ZStack/Host", "MemoryUsedInPercent", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal cpu_useds = getMetricData("ZStack/Host", "CPUAverageUsedUtilization", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal diskUsed = getMetricData("ZStack/Host", "DiskAllUsedCapacityInPercent", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal diskFreeBytes = getMetricData("ZStack/Host", "DiskAllFreeCapacityInBytes", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal diskUsedBytes = getMetricData("ZStack/Host", "DiskAllUsedCapacityInBytes", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal bandwidth_downstream = getMetricData("ZStack/Host", "NetworkAllOutBytes", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal bandwidth_upstream = getMetricData("ZStack/Host", "NetworkAllInBytes", HOST_UUID_PREFIX, uuid, token, platform);
            BigDecimal packet_rate = getMetricData("ZStack/Host", "NetworkAllInPackets", HOST_UUID_PREFIX, uuid, token, platform);

            // 获取CPU超分配置
            String cpuOverProvisioningValue = getResourceConfig("host", "cpu.overProvisioning.ratio", clusterUuid, token, platform);
            BigDecimal cpuOversold = new BigDecimal(cpuOverProvisioningValue != null ? cpuOverProvisioningValue : "1.0");
            hostData.setCpuOverPercent(cpuOversold);

            // 获取内存超售配置
            String memoryOverProvisioningValue = getResourceConfig("mevoco", "overProvisioning.memory", clusterUuid, token, platform);
            BigDecimal memoryOversold = new BigDecimal(memoryOverProvisioningValue != null ? memoryOverProvisioningValue : "1.0");
            hostData.setMemoryOverPercent(memoryOversold);

            // 获取预留内存配置
            String reservedMemoryValue = getResourceConfig("kvm", "reservedMemory", clusterUuid, token, platform);
            if (reservedMemoryValue != null) {
                BigDecimal reserved = new BigDecimal(reservedMemoryValue.replace("G", ""))
                        .multiply(new BigDecimal(1024 * 1024 * 1024));
                hostData.setReservedMemory(reserved);
            }

            // 获取主机网络接口
            QueryHostNetworkInterfaceAction macAction = new QueryHostNetworkInterfaceAction();
            macAction.conditions = asList("hostUuid=" + uuid);
            if (platform.getAkType() == 0) {
                macAction.sessionId = token;
            } else {
                macAction.accessKeyId = platform.getUsername();
                macAction.accessKeySecret = platform.getPassword();
            }
            macAction.call();


            String retainMemValue = getResourceConfig("kvm", "reservedMemory", uuid, token, platform);
            Long retainMem = 0L;
            if (retainMemValue != null) {
                retainMem = StringUtil.convertToKB(retainMemValue);
            } else {
                // 如果单个没查到查全部
                QueryGlobalConfigAction globalConfig = new QueryGlobalConfigAction();
                if (platform.getAkType() == 0) {
                    globalConfig.sessionId = token;
                } else {
                    globalConfig.accessKeyId = platform.getUsername();
                    globalConfig.accessKeySecret = platform.getPassword();
                }
                if (CollUtil.isNotEmpty(globalConfig.call().value.inventories)) {
                    for (Object list1 : globalConfig.call().value.inventories) {
                        JsonObject globalConfig1 = new Gson().toJsonTree(list1).getAsJsonObject();
                        if ("reservedMemory".equals(globalConfig1.get("name").getAsString())) {
                            retainMem = StringUtil.convertToKB(globalConfig1.get("value").getAsString());
                        }
                    }
                }
            }
            BigDecimal availableMem = NumberUtil.sub(Convert.toBigDecimal(availableMemoryCapacity), retainMem);
            if (availableMem.signum() < 0) {
                availableMem = new BigDecimal(0);
            }

            QueryUserTagAction actionTag = new QueryUserTagAction();
            actionTag.conditions = asList("resourceUuid=" + uuid, "resourceType=HostVO");
            if (platform.getAkType() == 0) {
                actionTag.sessionId = token;
            } else {
                actionTag.accessKeyId = platform.getUsername();
                actionTag.accessKeySecret = platform.getPassword();
            }
            QueryUserTagAction.Result restag = actionTag.call();

            List<String> tagValues = new ArrayList<>();
            for (Object inventory : restag.value.inventories) {
                JsonObject tagDO = GsonUtil.GSON.toJsonTree(inventory).getAsJsonObject().getAsJsonObject("tagPattern");
                String tag = getStringFromJson(tagDO, "name", "");
                String taguuid = getStringFromJson(tagDO, "uuid", "");
                if (StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(taguuid)) {
                    tagValues.add(tag + "&" + taguuid);
                }
            }
            String resultTag = tagValues.isEmpty() ? "" : (tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));

            hostData.setTag(resultTag);
            hostData.setModel("-");
            hostData.setBrandName("Zstack");
            hostData.setSerialNumber("-");
            //cpu类型（描述）
            hostData.setCpuType("-");
            hostData.setIpmi("-");
            hostData.setManufacturer("-");
            hostData.setUuid(uuid);
            hostData.setName(name);
            hostData.setState(state);
            hostData.setIp(ip);
            hostData.setStatus(status);
            hostData.setClusterUuid(clusterUuid);
            hostData.setClusterName(clusterName.toString());
            hostData.setTotalCpuCapacity(totalCpuCapacity);
            hostData.setTotalVirtualMemory(Convert.toBigDecimal(totalMemoryCapacity).multiply(memoryOversold));
            hostData.setAvailableCpuCapacity(availableCpuCapacity);
            hostData.setCpuSockets(cpuSockets);
            hostData.setArchitecture(architecture);
            hostData.setCpuNum(cpuNum);
            hostData.setCpuCommitRate(cpuCommitRate);
            hostData.setMemoryCommitRate(memCommitRate);
            hostData.setTotalMemoryCapacity(totalMemoryCapacity);
            hostData.setAvailableMemoryCapacity(Convert.toLong(availableMem));
            hostData.setBandwidthUpstream(bandwidth_upstream);
            hostData.setBandwidthDownstream(bandwidth_downstream);
            hostData.setMemoryUsed(memory_used);
            hostData.setPacketRate(packet_rate);
            hostData.setCpuUsed(cpu_useds);
            hostData.setDiskUsed(diskUsed);
            hostData.setDiskUsedBytes(diskUsedBytes);
            hostData.setDiskFreeBytes(diskFreeBytes);
            hostData.setTotalDiskCapacity(diskUsedBytes.add(diskFreeBytes));
            hostData.setTenantId(0L);
            hostData.setRegionId(platform.getRegionId());
            hostData.setPlatformId(platform.getPlatformId());
            hostData.setPlatformName(platform.getPlatformName());
            hostData.setDeleted(0);
            hostData.setTypeName("zstack");
            hostData.setCreateTime(createDate);
            hostData.setIsMaintain(0);
            hostDataS.add(hostData);
        } catch (Exception e) {
            log.error("host data error:{}", ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * 从JsonObject中安全获取BigDecimal值
     *
     * @param jsonObject   JSON对象
     * @param key          键
     * @param defaultValue 默认值
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalFromJson(JsonObject jsonObject, String key, BigDecimal defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsBigDecimal();
        }
        return defaultValue;
    }

    /**
     * 获取指标数据的通用方法
     *
     * @param namespace  命名空间
     * @param metricName 指标名称
     * @param uuidPrefix UUID前缀
     * @param uuid       UUID
     * @param token      会话令牌
     * @return 指标值
     */
    private BigDecimal getMetricData(String namespace, String metricName, String uuidPrefix, String uuid, String token, Platform platform) {
        try {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = metricName;
            action.labels = asList(uuidPrefix + uuid);
            if (platform.getAkType() == 0) {
                action.sessionId = token;
            } else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }

            GetMetricDataAction.Result result = action.call();
            if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                return dataObj.get("value").getAsBigDecimal();
            }
        } catch (Exception e) {
            log.error("获取指标数据失败: namespace={}, metricName={}, uuid={}", namespace, metricName, uuid, e);
        }
        return ZERO;
    }

    /**
     * 获取资源配置的通用方法
     *
     * @param category     类别
     * @param name         名称
     * @param resourceUuid 资源UUID
     * @param token        会话令牌
     * @return 配置值
     */
    private String getResourceConfig(String category, String name, String resourceUuid, String token, Platform platform) {
        try {
            GetResourceConfigAction action = new GetResourceConfigAction();
            action.category = category;
            action.name = name;
            action.resourceUuid = resourceUuid;
            if (platform.getAkType() == 0) {
                action.sessionId = token;
            } else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }

            GetResourceConfigAction.Result result = action.call();
            if (result != null && result.value != null) {
                return result.value.value;
            }
        } catch (Exception e) {
            log.error("获取资源配置失败: category={}, name={}, resourceUuid={}", category, name, resourceUuid, e);
        }
        return null;
    }

    /**
     * 从JsonObject中安全获取字符串值
     *
     * @param jsonObject   JSON对象
     * @param key          键
     * @param defaultValue 默认值
     * @return 字符串值
     */
    private String getStringFromJson(JsonObject jsonObject, String key, String defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsString();
        }
        return defaultValue;
    }

    /**
     * 从JsonObject中安全获取长整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 长整型值
     */
    private Long getLongFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsLong();
        }
        return 0L;
    }

    /**
     * 从JsonObject中安全获取整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 整型值
     */
    private Integer getIntFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsInt();
        }
        return 0;
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_HOST.code();
    }
}
