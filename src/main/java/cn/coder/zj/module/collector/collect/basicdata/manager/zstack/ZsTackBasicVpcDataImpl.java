package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackMultiPlatformRouter;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VpcData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.QueryClusterAction;
import org.zstack.sdk.QueryHostAction;
import org.zstack.sdk.QueryL3NetworkAction;
import org.zstack.sdk.QueryVpcRouterAction;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VPC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_VPC;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackBasicVpcDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform =(Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                try {
                    // 使用ZStackMultiPlatformRouter确保多平台配置隔离
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        List<VpcData> vpcDataList = new ArrayList<>();
                        String token = platform.getZsTackPlatform().getToken();
                        if (token == null && platform.getAkType() == 0) {
                            log.error("平台 {} token为空", platform.getPlatformName());
                            return null;
                        }

                        QueryVpcRouterAction action = new QueryVpcRouterAction();
                        if (platform.getAkType() == 0) {
                            action.sessionId = token;
                        } else {
                            action.accessKeyId = platform.getUsername();
                            action.accessKeySecret = platform.getPassword();
                        }
                        List<?> vpcList = action.call().value.inventories;

                        if (!vpcList.isEmpty()) {
                            processVpcList(vpcList, platform, token, vpcDataList);
                        }

                        if (!vpcDataList.isEmpty()) {
                            BasicCollectData build = BasicCollectData.builder().basicDataMap(vpcDataList)
                                    .metricsName(BASIC_VPC.code())
                                    .build();
                            String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                            log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                            message.setType(ClusterMsg.MessageType.BASIC);
                            message.setData(GsonUtil.GSON.toJson(build));
                            message.setTime(System.currentTimeMillis());
                            // 发送该平台的数据
                            sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        }

                        return vpcDataList;
                    });
                } catch (Exception e) {
                    log.error("平台 {} VPC数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                }
            });
        }
        log.info("collectHostData name: {}", Thread.currentThread().getName());

    }

    private void processVpcList(List<?> vpcList, Platform platform, String token, List<VpcData> vpcDataList) {
        for (Object vpc : vpcList) {
            AtomicReference<String> clusterName = new AtomicReference<>();
            JsonObject jsonObject = GsonUtil.GSON.toJsonTree(vpc).getAsJsonObject();
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            Date date = DateUtil.date(new Date(jsonObject.get("createDate").getAsString()));
            String name = jsonObject.get("name").getAsString();
            Integer cpu_num = jsonObject.get("cpuNum").getAsInt();
            Long memorySize = jsonObject.get("memorySize").getAsLong();
            String architecture = jsonObject.get("architecture").getAsString();
            String status = jsonObject.get("status").getAsString();
            String state = jsonObject.get("state").getAsString();
            String managementNetworkUuid = jsonObject.get("managementNetworkUuid").getAsString();
            String l3NetworkUuid = jsonObject.get("defaultRouteL3NetworkUuid").getAsString();
            String clusterUuid = jsonObject.get("clusterUuid").getAsString();
            String hypervisorType = jsonObject.get("hypervisorType").getAsString();
            String hostUuid = jsonObject.get("lastHostUuid").getAsString();
            Long platformId = platform.getPlatformId();
            String platformName = platform.getPlatformName();
            AtomicReference<String> l3NetworkName = new AtomicReference<>("");
            // 根据hostUuid 去查名称
            AtomicReference<String> hostName = new AtomicReference<>("");
            // 取ipv4的mac地址
            String mac = "";
            // IPv4 地址根据l3networkUuid 去查；
            String ip = "";
            // 根据dns 循环去取
            String dns = "";
            // 根据managementNetwork中的uuid 相同取vmics中uuid 相同的ip地址，ip同理
            String managementNetworkip = "";
            // 获取集群名称
            QueryClusterAction clusters = new QueryClusterAction();
            clusters.conditions = asList("hypervisorType=KVM");
            if (platform.getAkType()==0){
                clusters.sessionId = token;
            }else{
                clusters.accessKeyId = platform.getUsername();
                clusters.accessKeySecret = platform.getPassword();
            }
            clusters.call().value.inventories.forEach(cluster -> {
                JsonObject clusterObj = GsonUtil.GSON.toJsonTree(cluster).getAsJsonObject();
                if (clusterUuid.equals(clusterObj.get("uuid").getAsString())) {
                    clusterName.set(clusterObj.get("name").getAsString());
                }
            });
            // 宿主机名称
            QueryHostAction hostAction = new QueryHostAction();
            if (platform.getAkType()==0){
                hostAction.sessionId = token;
            }else{
                hostAction.accessKeyId = platform.getUsername();
                hostAction.accessKeySecret = platform.getPassword();
            }
            hostAction.conditions = asList("uuid=" + hostUuid);
            hostAction.call().value.inventories.forEach(host -> {
                JsonObject hostObj = GsonUtil.GSON.toJsonTree(host).getAsJsonObject();
                hostName.set(hostObj.get("name").getAsString());
            });

            // 三级网络名称
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            action.conditions = asList("uuid=" + l3NetworkUuid);
            if (platform.getAkType()==0){
                action.sessionId = token;
            }else{
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }
            QueryL3NetworkAction.Result res = action.call();
            action.call().value.inventories.forEach(l3Network -> {
                JsonObject l3NetworkObj = GsonUtil.GSON.toJsonTree(l3Network).getAsJsonObject();
                l3NetworkName.set(l3NetworkObj.get("name").getAsString());
            });


            // dns
            JsonArray jsonArrayDns = jsonObject.getAsJsonArray("dns");
            if (jsonArrayDns != null && !jsonArrayDns.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                for (int j = 0; j < jsonArrayDns.size(); j++) {
                    JsonObject jsonElement = jsonArrayDns.get(j).getAsJsonObject();
                    if (j > 0) {
                        sb.append(",");
                    }
                    sb.append(jsonElement.get("dns").getAsString());
                }
                dns = sb.toString();
            }
            //
            JsonArray jsonArrayVmNics = jsonObject.getAsJsonArray("vmNics");
            for (int j = 0; j < jsonArrayVmNics.size(); j++) {
                JsonObject asJsonObject = jsonArrayVmNics.get(j).getAsJsonObject();
                if (l3NetworkUuid.equals(asJsonObject.get("l3NetworkUuid").getAsString())) {
                    ip = asJsonObject.get("ip").getAsString();
                    mac = asJsonObject.get("mac").getAsString();
                    managementNetworkip = asJsonObject.get("ip").getAsString();
                }
            }
            vpcDataList.add(VpcData.builder().uuid(uuid)
                    .name(name)
                    .cpuNum(cpu_num)
                    .memorySize(memorySize)
                    .architecture(architecture)
                    .dns(dns)
                    .status(status)
                    .state(state)
                    .l3NetworkUuid(l3NetworkUuid)
                    .l3NetworkName(l3NetworkName.get())
                    .ip(ip)
                    .managementNetworkUuid(managementNetworkUuid)
                    .managementNetworkIp(managementNetworkip)
                    .clusterUuid(clusterUuid)
                    .clusterName(clusterName.toString())
                    .hypervisorType(hypervisorType)
                    .mac(mac)
                    .hostUuid(hostUuid)
                    .hostName(hostName.get())
                    .platformId(platformId)
                    .platformName(platformName)
                    .tenantId(0L)
                    .createTime(date).build());
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_VPC.code();
    }
}
